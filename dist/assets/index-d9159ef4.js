import{r,a as gs,b as qe}from"./vendor-280e31ee.js";import{N as ss,u as He,a as ps,L as fs,O as ys,b as bs,B as js,R as ws,c as ue}from"./router-208768c5.js";import{C as Ns,X as vs,A as ks,I as Ss,M as As,U as De,S as We,L as Ps,a as ze,b as Cs,T as ve,c as je,B as ts,d as Re,e as as,f as Ds,E as Rs,g as $s,h as Ts,F as Ge,i as Me,H as Qe,D as xe,j as Es,k as Ls,P as Se,R as oe,l as rs,m as $e,n as _s,o as Us,p as Fs,q as Oe,r as Ve,s as Bs,t as Is,u as ns,K as Ms,v as Os,w as Be,x as ls,y as Hs,z as zs}from"./icons-9d7a79a3.js";import{R as Ae,B as os,C as Te,X as Ee,Y as Le,T as Pe,a as is,L as _e,b as Je,c as Ye,P as Ks,d as Vs,e as qs,S as Xe,f as Ws}from"./charts-2d8bc326.js";(function(){const n=document.createElement("link").relList;if(n&&n.supports&&n.supports("modulepreload"))return;for(const l of document.querySelectorAll('link[rel="modulepreload"]'))p(l);new MutationObserver(l=>{for(const c of l)if(c.type==="childList")for(const d of c.addedNodes)d.tagName==="LINK"&&d.rel==="modulepreload"&&p(d)}).observe(document,{childList:!0,subtree:!0});function a(l){const c={};return l.integrity&&(c.integrity=l.integrity),l.referrerPolicy&&(c.referrerPolicy=l.referrerPolicy),l.crossOrigin==="use-credentials"?c.credentials="include":l.crossOrigin==="anonymous"?c.credentials="omit":c.credentials="same-origin",c}function p(l){if(l.ep)return;l.ep=!0;const c=a(l);fetch(l.href,c)}})();var cs={exports:{}},Ke={};/**
 * @license React
 * react-jsx-runtime.production.min.js
 *
 * Copyright (c) Facebook, Inc. and its affiliates.
 *
 * This source code is licensed under the MIT license found in the
 * LICENSE file in the root directory of this source tree.
 */var Js=r,Ys=Symbol.for("react.element"),Gs=Symbol.for("react.fragment"),Qs=Object.prototype.hasOwnProperty,Xs=Js.__SECRET_INTERNALS_DO_NOT_USE_OR_YOU_WILL_BE_FIRED.ReactCurrentOwner,Zs={key:!0,ref:!0,__self:!0,__source:!0};function ds(s,n,a){var p,l={},c=null,d=null;a!==void 0&&(c=""+a),n.key!==void 0&&(c=""+n.key),n.ref!==void 0&&(d=n.ref);for(p in n)Qs.call(n,p)&&!Zs.hasOwnProperty(p)&&(l[p]=n[p]);if(s&&s.defaultProps)for(p in n=s.defaultProps,n)l[p]===void 0&&(l[p]=n[p]);return{$$typeof:Ys,type:s,key:c,ref:d,props:l,_owner:Xs.current}}Ke.Fragment=Gs;Ke.jsx=ds;Ke.jsxs=ds;cs.exports=Ke;var e=cs.exports,ms,Ze=gs;ms=Ze.createRoot,Ze.hydrateRoot;const us=()=>window.location.origin,Ie=async(s,n={})=>{const a=localStorage.getItem("adminToken"),p=us(),l={headers:{"Content-Type":"application/json",...a&&{Authorization:`Bearer ${a}`}}},c={...l,...n,headers:{...l.headers,...n.headers}},d=await fetch(`${p}${s}`,c);if(d.status===401){let y="Authentication failed. Please login again.";try{const $=await d.json();$.message&&$.message.includes("expired")?y="Your session has expired. Please login again.":$.message&&$.message.includes("token")&&(y="Invalid session. Please login again.")}catch{}throw window.dispatchEvent(new CustomEvent("auth-error",{detail:{message:y}})),new Error(y)}return d},xs=r.createContext(),we=()=>{const s=r.useContext(xs);if(!s)throw new Error("useAuth must be used within an AuthProvider");return s},et=({children:s})=>{const[n,a]=r.useState(!1),[p,l]=r.useState(null),[c,d]=r.useState(!0),[y,$]=r.useState(null),b=(j="Session expired. Please login again.")=>{console.warn("Auto-logout triggered:",j),window.location.pathname!=="/login"&&(localStorage.setItem("redirectAfterLogin",window.location.pathname),$(window.location.pathname)),localStorage.removeItem("adminToken"),localStorage.removeItem("adminUser"),a(!1),l(null),window.toast&&window.toast(j,"warning"),window.location.href="/login"};r.useEffect(()=>{const j=localStorage.getItem("adminToken"),w=localStorage.getItem("adminUser"),i=localStorage.getItem("redirectAfterLogin");j&&w&&(a(!0),l(JSON.parse(w))),i&&$(i),d(!1);const x=D=>{const{message:P}=D.detail;b(P)};return window.addEventListener("auth-error",x),()=>{window.removeEventListener("auth-error",x)}},[]);const k={isAuthenticated:n,user:p,login:async(j,w)=>{try{const i=us();console.log(`[AUTH] ${new Date().toISOString()} - Attempting login to:`,`${i}/api/auth/login`),console.log(`[AUTH] Login attempt for email: ${j?j.substring(0,3)+"***":"undefined"}`);const x=new AbortController,D=setTimeout(()=>x.abort(),3e4),P=await fetch(`${i}/api/auth/login`,{method:"POST",headers:{"Content-Type":"application/json"},body:JSON.stringify({email:j,password:w}),signal:x.signal});clearTimeout(D),console.log(`[AUTH] Response status: ${P.status}`),console.log("[AUTH] Response headers:",Object.fromEntries(P.headers.entries()));const M=await P.text();console.log("[AUTH] Response text (first 200 chars):",M.substring(0,200));let U;try{U=JSON.parse(M),console.log("[AUTH] Parsed response data:",{success:U.success,message:U.message})}catch(R){console.error("[AUTH] Failed to parse response as JSON:",R),console.error("[AUTH] Raw response text:",M);let S="Server returned invalid response. Please try again.";return M.includes("Internal Server Error")?S="Server is experiencing issues. Please try again in a few moments.":M.includes("timeout")?S="Request timed out. Please check your connection and try again.":M.includes("Database")?S="Database connection issue. Please try again.":P.status>=500?S="Server error occurred. Please try again later.":P.status===404&&(S="Login service not found. Please contact support."),{success:!1,message:S}}if(P.ok&&U.success){console.log("[AUTH] Login successful, storing user data"),localStorage.setItem("adminToken",U.token),localStorage.setItem("adminUser",JSON.stringify(U.user)),a(!0),l(U.user);const R=localStorage.getItem("redirectAfterLogin");return R?(localStorage.removeItem("redirectAfterLogin"),$(null),{success:!0,redirectTo:R}):{success:!0}}else return console.log(`[AUTH] Login failed: ${U.message||"Unknown error"}`),{success:!1,message:U.message||"Login failed. Please check your credentials."}}catch(i){console.error("[AUTH] Login error:",{name:i.name,message:i.message,stack:i.stack});let x="Login failed. Please try again.";return i.name==="AbortError"?x="Request timed out. Please check your connection and try again.":i.message.includes("fetch")?x="Network error. Please check your connection and try again.":i.message.includes("NetworkError")&&(x="Network connection failed. Please try again."),{success:!1,message:x}}},logout:(j=!1)=>{j&&window.location.pathname!=="/login"&&(localStorage.setItem("redirectAfterLogin",window.location.pathname),$(window.location.pathname)),localStorage.removeItem("adminToken"),localStorage.removeItem("adminUser"),a(!1),l(null)},autoLogout:b,loading:c,redirectPath:y};return e.jsx(xs.Provider,{value:k,children:s})},st=({message:s,type:n="info",duration:a=3e3,onClose:p})=>{const[l,c]=r.useState(!0);r.useEffect(()=>{const b=setTimeout(()=>{c(!1),setTimeout(p,300)},a);return()=>clearTimeout(b)},[a,p]);const d={success:Ns,error:vs,warning:ks,info:Ss},y={success:"bg-green-50 text-green-800 border-green-200",error:"bg-red-50 text-red-800 border-red-200",warning:"bg-yellow-50 text-yellow-800 border-yellow-200",info:"bg-blue-50 text-blue-800 border-blue-200"},$=d[n];return e.jsx("div",{className:`fixed top-4 right-4 z-50 transition-all duration-300 ${l?"opacity-100 translate-y-0":"opacity-0 -translate-y-2"}`,children:e.jsxs("div",{className:`flex items-center p-4 rounded-lg border shadow-lg ${y[n]}`,children:[e.jsx($,{className:"h-5 w-5 mr-3 flex-shrink-0"}),e.jsx("span",{className:"font-medium",children:s})]})})},tt=({children:s})=>{const[n,a]=r.useState([]),p=(c,d="info",y=3e3)=>{const $=Date.now();a(b=>[...b,{id:$,message:c,type:d,duration:y}])},l=c=>{a(d=>d.filter(y=>y.id!==c))};return qe.useEffect(()=>{window.toast=p},[]),e.jsxs(e.Fragment,{children:[s,n.map(c=>e.jsx(st,{message:c.message,type:c.type,duration:c.duration,onClose:()=>l(c.id)},c.id))]})},at=({children:s})=>{const{isAuthenticated:n,loading:a}=we();return a?e.jsx("div",{className:"min-h-screen flex items-center justify-center",children:e.jsx("div",{className:"animate-spin rounded-full h-8 w-8 border-b-2 border-blue-600"})}):n?s:e.jsx(ss,{to:"/login",replace:!0})},rt=()=>{const[s,n]=r.useState(!1),a=r.useRef(null),{logout:p,user:l}=we(),c=He();r.useEffect(()=>{const b=f=>{a.current&&!a.current.contains(f.target)&&n(!1)};return document.addEventListener("mousedown",b),()=>{document.removeEventListener("mousedown",b)}},[]);const d=()=>{p(),c("/login"),n(!1)},y=b=>{c(b),n(!1)},$=()=>{const b=[{label:"Profile",icon:De,onClick:()=>y("/profile")}];return((l==null?void 0:l.role)==="admin"||(l==null?void 0:l.role)==="superadmin")&&b.push({label:"Settings",icon:We,onClick:()=>y("/settings")}),b.push({label:"Logout",icon:Ps,onClick:d,className:"text-red-600 hover:bg-red-50"}),b};return e.jsxs("div",{className:"relative",ref:a,children:[e.jsx("button",{onClick:()=>n(!s),className:"flex items-center p-2 text-gray-600 hover:bg-[#edf1f7] hover:text-gray-800 rounded-md transition-colors",title:"More options",children:e.jsx(As,{className:"h-4 w-4"})}),s&&e.jsx("div",{className:"absolute bottom-full right-0 mb-2 w-48 bg-white rounded-lg shadow-lg border border-gray-200 py-1 z-50",children:$().map((b,f)=>{const L=b.icon;return e.jsxs("button",{onClick:b.onClick,className:`w-full flex items-center px-4 py-2 text-sm text-left hover:bg-gray-50 transition-colors ${b.className||"text-gray-700"}`,children:[e.jsx(L,{className:"h-4 w-4 mr-3"}),b.label]},f)})})]})},nt=s=>{let n=0;if(s.length===0)return n.toString();for(let a=0;a<s.length;a++){const p=s.charCodeAt(a);n=(n<<5)-n+p,n=n&n}return Math.abs(n).toString(16)},lt=(s,n=32)=>`https://www.gravatar.com/avatar/${nt(s)}?s=${n}&d=identicon`,ot=()=>{const{user:s}=we(),n=ps(),[a,p]=r.useState(!1),l=()=>{const c=[{name:"Dashboard",path:"/dashboard",icon:Cs},{name:"Plugin Rank",path:"/plugin-rank",icon:ve},{name:"Keyword Analysis",path:"/keyword-analysis",icon:je},{name:"Plugin Data Analysis",path:"/analytics",icon:ts}];return((s==null?void 0:s.role)==="admin"||(s==null?void 0:s.role)==="superadmin")&&c.push({name:"Team Members",path:"/users",icon:Re}),c};return e.jsxs("div",{className:"min-h-screen bg-gray-50",children:[a&&e.jsx("div",{className:"fixed inset-0 bg-black bg-opacity-50 z-40 lg:hidden",onClick:()=>p(!1)}),e.jsxs("div",{className:`fixed inset-y-0 left-0 z-50 w-64 bg-white shadow-lg transform transition-transform duration-300 ease-in-out lg:translate-x-0 ${a?"translate-x-0":"-translate-x-full"}`,children:[e.jsxs("div",{className:"flex items-center justify-between h-16 px-6 border-b",children:[e.jsx("img",{src:"/wpdev.png",className:"",alt:"WPDeveloper Logo"}),e.jsx("button",{onClick:()=>p(!1),className:"lg:hidden p-2 rounded-md hover:bg-gray-100",children:e.jsx(ze,{className:"h-5 w-5"})})]}),e.jsx("nav",{className:"mt-6",children:l().map(c=>{const d=c.icon,y=n.pathname===c.path;return e.jsxs(fs,{to:c.path,onClick:()=>p(!1),className:`flex items-center px-6 py-3 text-sm font-medium transition-colors ${y?"bg-blue-50 text-blue-600 border-r-2 border-blue-600":"text-gray-600 hover:bg-gray-50 hover:text-gray-900"}`,children:[e.jsx(d,{className:"h-5 w-5 mr-3"}),c.name]},c.path)})}),e.jsx("div",{className:"absolute bottom-0 left-0 right-0 p-6 border-t",children:e.jsxs("div",{className:"flex items-center justify-between",children:[e.jsxs("div",{className:"flex items-center",children:[e.jsx("div",{className:"flex-shrink-0 w-8 h-8 rounded-full overflow-hidden",children:e.jsx("img",{src:(s==null?void 0:s.profileImage)||lt((s==null?void 0:s.email)||""),alt:"Profile",className:"w-full h-full object-cover"})}),e.jsxs("div",{className:"ml-3",children:[e.jsx("p",{className:"text-sm font-medium text-gray-700",children:(s==null?void 0:s.name)||"Admin User"}),e.jsx("p",{className:"text-xs text-gray-500 capitalize",children:(s==null?void 0:s.role)||"member"})]})]}),e.jsx(rt,{})]})})]}),e.jsx("div",{className:"lg:pl-64",children:e.jsx("main",{className:"p-6",children:e.jsx(ys,{})})})]})},it=()=>{const[s,n]=r.useState(""),[a,p]=r.useState(""),[l,c]=r.useState(!1),[d,y]=r.useState(!1),[$,b]=r.useState(""),{login:f}=we(),L=He(),k=async j=>{j.preventDefault(),b(""),y(!0);const w=await f(s,a);if(w.success){const i=w.redirectTo||"/dashboard";L(i)}else b(w.message||"Login failed");y(!1)};return e.jsx("div",{className:"min-h-screen bg-gradient-to-br from-blue-50 to-indigo-100 flex items-center justify-center p-4",children:e.jsx("div",{className:"max-w-md w-full space-y-8",children:e.jsxs("div",{className:"bg-white rounded-xl shadow-xl p-8",children:[e.jsxs("div",{className:"text-center mb-8",children:[e.jsx("div",{className:"mx-auto h-16 w-16 bg-blue-500 rounded-full flex items-center justify-center mb-4",children:e.jsx("img",{src:"/wpdev_logo.jpeg",className:"h-16 w-16 rounded-full border"})}),e.jsx("h2",{className:"text-2xl font-bold text-gray-900",children:"Welcome Back"}),e.jsx("p",{className:"text-gray-600 mt-2",children:"Sign in to your admin account"})]}),e.jsxs("form",{onSubmit:k,className:"space-y-6",children:[$&&e.jsx("div",{className:"bg-red-50 border border-red-200 text-red-600 px-4 py-3 rounded-lg text-sm",children:$}),e.jsxs("div",{children:[e.jsx("label",{htmlFor:"email",className:"block text-sm font-medium text-gray-700 mb-2",children:"Email Address"}),e.jsxs("div",{className:"relative",children:[e.jsx(as,{className:"absolute left-3 top-1/2 transform -translate-y-1/2 h-5 w-5 text-gray-400"}),e.jsx("input",{id:"email",type:"email",value:s,onChange:j=>n(j.target.value),className:"block w-full pl-10 pr-3 py-3 border border-gray-300 rounded-lg focus:outline-none focus:ring-2 focus:ring-blue-500 focus:border-transparent",placeholder:"Enter your email",required:!0})]})]}),e.jsxs("div",{children:[e.jsx("label",{htmlFor:"password",className:"block text-sm font-medium text-gray-700 mb-2",children:"Password"}),e.jsxs("div",{className:"relative",children:[e.jsx(Ds,{className:"absolute left-3 top-1/2 transform -translate-y-1/2 h-5 w-5 text-gray-400"}),e.jsx("input",{id:"password",type:l?"text":"password",value:a,onChange:j=>p(j.target.value),className:"block w-full pl-10 pr-10 py-3 border border-gray-300 rounded-lg focus:outline-none focus:ring-2 focus:ring-blue-500 focus:border-transparent",placeholder:"Enter your password",required:!0}),e.jsx("button",{type:"button",onClick:()=>c(!l),className:"absolute right-3 top-1/2 transform -translate-y-1/2 text-gray-400 hover:text-gray-600",children:l?e.jsx(Rs,{className:"h-5 w-5"}):e.jsx($s,{className:"h-5 w-5"})})]})]}),e.jsx("button",{type:"submit",disabled:d,className:"w-full bg-blue-600 text-white py-3 px-4 rounded-lg hover:bg-blue-700 focus:outline-none focus:ring-2 focus:ring-blue-500 focus:ring-offset-2 disabled:opacity-50 disabled:cursor-not-allowed transition-colors font-medium",children:d?"Signing in...":"Sign In"})]})]})})})},ye=({isOpen:s,onClose:n,title:a,children:p,maxWidth:l="max-w-xl",fixedHeight:c=!1})=>(r.useEffect(()=>{const d=y=>{y.key==="Escape"&&n()};return s&&(document.addEventListener("keydown",d),document.body.style.overflow="hidden"),()=>{document.removeEventListener("keydown",d),document.body.style.overflow="unset"}},[s,n]),s?e.jsx("div",{className:"fixed inset-0 z-50 overflow-y-auto",children:e.jsxs("div",{className:"flex min-h-screen items-center justify-center p-4",children:[e.jsx("div",{className:"fixed inset-0 bg-black bg-opacity-50 backdrop-blur-sm transition-opacity",onClick:n}),e.jsxs("div",{className:`relative bg-white rounded-lg shadow-xl ${l} w-full mx-4 transform transition-all ${c?"h-[90vh] flex flex-col":""}`,children:[e.jsxs("div",{className:"flex items-center justify-between p-6 border-b flex-shrink-0",children:[e.jsx("h3",{className:"text-lg font-semibold text-gray-900",children:a}),e.jsx("button",{onClick:n,className:"p-1 hover:bg-gray-100 rounded-full transition-colors",children:e.jsx(ze,{className:"h-5 w-5 text-gray-500"})})]}),e.jsx("div",{className:`p-6 ${c?"flex-1 overflow-y-auto":""}`,children:p})]})]})}):null),ct=({isOpen:s,onClose:n,plugin:a})=>{const p=He(),l=r.useRef(null),[c,d]=r.useState([]),[y,$]=r.useState([]),[b,f]=r.useState(0),[L,k]=r.useState(0),[j,w]=r.useState([]),[i,x]=r.useState({}),[D,P]=r.useState(null),[M,U]=r.useState({}),[R,S]=r.useState(""),[h,g]=r.useState(""),[v,T]=r.useState(!1),[_,E]=r.useState(!0),[I,O]=r.useState(!0),[V,G]=r.useState(!0),[X,te]=r.useState(!0),[ie,he]=r.useState(0),ne=(u,F)=>{he(F)},Ne=async()=>{if(a)try{E(!0);const u=localStorage.getItem("adminToken"),W=await fetch(`https://pluginsight.vercel.app/api/analytics/download-data/${a.slug}?days=15`,{headers:{Authorization:`Bearer ${u}`}});if(!W.ok)throw new Error("Failed to fetch download data");const H=await W.json();if(H.success&&H.downloadData){const ee=H.downloadData.map(o=>({date:new Date(o.date).toLocaleDateString("en-GB",{day:"2-digit",month:"2-digit"}),downloads:o.downloads,fullDate:o.date}));d(ee)}else d([])}catch(u){console.error("Error fetching download data:",u),d([])}finally{E(!1)}},ge=async()=>{if(a)try{O(!0);const u=localStorage.getItem("adminToken"),W=await fetch(`https://pluginsight.vercel.app/api/analytics/plugin-info/${a.slug}`,{headers:{Authorization:`Bearer ${u}`}});if(!W.ok)throw new Error("Failed to fetch plugin information");const H=await W.json();H.success&&H.ratings?($(H.ratings),f(H.totalRatings),k(H.averageRating||0)):($([]),f(0),k(0))}catch(u){console.error("Error fetching ratings data:",u),$([]),f(0),k(0)}finally{O(!1)}},pe=async()=>{if(a)try{G(!0);const u=localStorage.getItem("adminToken"),W=await fetch(`https://pluginsight.vercel.app/api/analytics/rank-history/${a.slug}?days=15`,{headers:{Authorization:`Bearer ${u}`}});if(!W.ok)throw new Error("Failed to fetch rank history");const H=await W.json();if(H.success&&H.rankHistory){const ee=H.rankHistory.map(o=>({date:o.date,rank:o.rank,fetchedAt:o.fetchedAt}));w(ee)}else w([])}catch(u){console.error("Error fetching rank history:",u),w([])}finally{G(!1)}},me=async()=>{if(a)try{te(!0);const u=localStorage.getItem("adminToken"),W=await fetch(`https://pluginsight.vercel.app/api/analytics/plugin-versions/${a.slug}`,{headers:{Authorization:`Bearer ${u}`}});if(!W.ok)throw new Error("Failed to fetch plugin versions");const H=await W.json();H.success?(x(H.versions||{}),P(H.currentVersion),U(H.oldVersions||{})):(x({}),P(null),U({}))}catch(u){console.error("Error fetching versions data:",u),x({}),P(null),U({})}finally{te(!1)}};r.useEffect(()=>{s&&a&&(Ne(),ge(),pe(),me())},[s,a]),r.useEffect(()=>{const u=F=>{l.current&&!l.current.contains(F.target)&&(T(!1),g(""))};if(v)return document.addEventListener("mousedown",u),()=>{document.removeEventListener("mousedown",u)}},[v]);const le=u=>{var F;return u?((F=u.split(/[-–:]|&#8211;/)[0])==null?void 0:F.trim())||u:""},ce=()=>!M||Object.keys(M).length===0?[]:Object.keys(M).filter(u=>u.toLowerCase()==="trunk"||u===D?!1:h.trim()?u.toLowerCase().includes(h.toLowerCase()):!0).sort((u,F)=>{const W=o=>o.split(".").map(A=>parseInt(A)||0),H=W(u),ee=W(F);for(let o=0;o<Math.max(H.length,ee.length);o++){const A=(ee[o]||0)-(H[o]||0);if(A!==0)return A}return 0}),be=u=>{S(u),T(!1),g("")},m=()=>{T(!v),v||g("")},B=["#10B981","#3B82F6","#F59E0B","#EF4444","#8B5CF6"],Z=u=>{const F=Math.PI/180,{cx:W,cy:H,midAngle:ee,innerRadius:o,outerRadius:A,startAngle:N,endAngle:C,fill:q,payload:Y,percent:se,value:Ue}=u,ke=Math.sin(-F*ee),de=Math.cos(-F*ee),t=W+(A+10)*de,z=H+(A+10)*ke,J=W+(A+30)*de,Q=H+(A+30)*ke,K=J+(de>=0?1:-1)*22,ae=Q,re=de>=0?"start":"end";return e.jsxs("g",{children:[e.jsxs("text",{x:W,y:H,dy:8,textAnchor:"middle",fill:q,children:[Y.stars,"★"]}),e.jsx(Xe,{cx:W,cy:H,innerRadius:o,outerRadius:A,startAngle:N,endAngle:C,fill:q}),e.jsx(Xe,{cx:W,cy:H,startAngle:N,endAngle:C,innerRadius:A+6,outerRadius:A+10,fill:q}),e.jsx("path",{d:`M${t},${z}L${J},${Q}L${K},${ae}`,stroke:q,fill:"none"}),e.jsx("circle",{cx:K,cy:ae,r:2,fill:q,stroke:"none"}),e.jsx("text",{x:K+(de>=0?1:-1)*12,y:ae,textAnchor:re,fill:"#333",children:`${Ue} ratings`}),e.jsx("text",{x:K+(de>=0?1:-1)*12,y:ae,dy:18,textAnchor:re,fill:"#999",children:`(${(se*100).toFixed(1)}%)`})]})};return s?e.jsx("div",{className:"fixed inset-0 bg-black bg-opacity-50 flex items-center justify-center z-50 p-4",children:e.jsxs("div",{className:"bg-white rounded-xl shadow-2xl max-w-6xl w-full max-h-[95vh] flex flex-col",children:[e.jsxs("div",{className:"flex items-center justify-between p-6 border-b border-gray-200 flex-shrink-0",children:[e.jsxs("div",{className:"flex items-center space-x-3",children:[e.jsx("div",{className:"w-12 h-12 bg-blue-100 rounded-lg flex items-center justify-center",children:e.jsx(Ts,{className:"h-6 w-6 text-blue-600"})}),e.jsxs("div",{children:[e.jsxs("h2",{className:"text-2xl font-bold text-gray-900",children:[le((a==null?void 0:a.displayName)||(a==null?void 0:a.name))," ","Analytics"]}),e.jsx("p",{className:"text-sm text-gray-600",children:"Download trends and rating analysis"})]})]}),e.jsxs("div",{className:"flex items-center space-x-3",children:[e.jsxs("button",{onClick:()=>p(`/plugin-details/${a==null?void 0:a.slug}`),className:"flex items-center space-x-2 px-3 py-2 text-sm bg-green-100 hover:bg-green-200 text-green-700 rounded-lg transition-colors",title:"View Plugin Details",children:[e.jsx(Ge,{className:"h-4 w-4"}),e.jsx("span",{children:"Plugin Details"})]}),e.jsx("button",{onClick:n,className:"text-gray-400 hover:text-gray-600 transition-colors p-2",children:e.jsx(ze,{className:"h-6 w-6"})})]})]}),e.jsxs("div",{className:"flex-1 p-6 space-y-4 overflow-y-auto",children:[e.jsxs("div",{className:"bg-gray-50 rounded-lg p-6",children:[e.jsxs("h3",{className:"text-lg font-semibold text-gray-900 mb-4 flex items-center",children:[e.jsx(ve,{className:"h-5 w-5 mr-2 text-blue-600"}),"Download Trends (Last 15 Days)"]}),_?e.jsx("div",{className:"h-64 flex items-center justify-center",children:e.jsx("div",{className:"animate-spin rounded-full h-8 w-8 border-b-2 border-blue-600"})}):c.length>0?e.jsx(Ae,{width:"100%",height:300,children:e.jsxs(os,{data:c,children:[e.jsx(Te,{strokeDasharray:"3 3"}),e.jsx(Ee,{dataKey:"date"}),e.jsx(Le,{}),e.jsx(Pe,{formatter:u=>[u.toLocaleString(),"Downloads"],labelFormatter:u=>`Date: ${u}`}),e.jsx(is,{dataKey:"downloads",fill:"#3B82F6",children:e.jsx(_e,{dataKey:"downloads",position:"top",fontSize:10,formatter:u=>u.toLocaleString()})})]})}):e.jsx("div",{className:"h-64 flex items-center justify-center text-gray-500",children:"No download data available"})]}),e.jsxs("div",{className:"bg-gray-50 rounded-lg p-6",children:[e.jsxs("h3",{className:"text-lg font-semibold text-gray-900 mb-4 flex items-center",children:[e.jsx(ve,{className:"h-5 w-5 mr-2 text-purple-600"}),"15-Day Rank Change"]}),V?e.jsx("div",{className:"h-64 flex items-center justify-center",children:e.jsx("div",{className:"animate-spin rounded-full h-8 w-8 border-b-2 border-purple-600"})}):j.length>0?e.jsx(Ae,{width:"100%",height:300,children:e.jsxs(Je,{data:j,children:[e.jsx(Te,{strokeDasharray:"3 3"}),e.jsx(Ee,{dataKey:"date"}),e.jsx(Le,{domain:["dataMin - 10","dataMax + 10"],reversed:!0,tickFormatter:u=>`#${u}`}),e.jsx(Pe,{formatter:u=>[`#${u}`,"Rank"],labelFormatter:u=>`Date: ${u}`}),e.jsx(Ye,{type:"monotone",dataKey:"rank",stroke:j.length>1&&j[j.length-1].rank<j[0].rank?"#10B981":"#EF4444",strokeWidth:2,dot:{fill:j.length>1&&j[j.length-1].rank<j[0].rank?"#10B981":"#EF4444",strokeWidth:2,r:4},activeDot:{r:6,strokeWidth:2},children:e.jsx(_e,{dataKey:"rank",position:"top",formatter:u=>`#${u}`,style:{fontSize:"12px",fill:"#374151",fontWeight:"500"}})})]})}):e.jsx("div",{className:"h-64 flex items-center justify-center text-gray-500",children:"No rank history data available"})]}),e.jsxs("div",{className:"grid grid-cols-1 lg:grid-cols-3 gap-6",children:[e.jsxs("div",{className:"lg:col-span-2 bg-gray-50 rounded-lg p-6",children:[e.jsxs("div",{className:"flex items-center justify-between mb-6",children:[e.jsxs("h3",{className:"text-lg font-semibold text-gray-900 flex items-center",children:[e.jsx(Me,{className:"h-5 w-5 mr-2 text-yellow-600"}),"Rating Distribution"]}),e.jsx("div",{className:"text-right",children:e.jsxs("div",{className:"text-lg font-bold text-gray-900 flex items-center",children:[L?(L/20).toFixed(1):"N/A"," ⭐"]})})]}),I?e.jsx("div",{className:"h-64 flex items-center justify-center",children:e.jsx("div",{className:"animate-spin rounded-full h-8 w-8 border-b-2 border-yellow-600"})}):y.length>0?e.jsxs("div",{className:"flex gap-2",children:[e.jsx("div",{className:"bg-white rounded-lg p-4 flex-1",children:e.jsx("div",{className:"flex justify-center",children:e.jsx(Ae,{width:200,height:200,children:e.jsxs(Ks,{children:[e.jsx(Vs,{activeIndex:ie,activeShape:Z,data:[...y].sort((u,F)=>F.stars-u.stars),cx:"50%",cy:"50%",innerRadius:40,outerRadius:60,fill:"#8884d8",dataKey:"value",onMouseEnter:ne,children:y.map((u,F)=>e.jsx(qs,{fill:B[F%B.length]},`cell-${F}`))}),e.jsx(Pe,{formatter:(u,F,W)=>[`${u} ratings`,`${W.payload.stars} Star${W.payload.stars!==1?"s":""}`]})]})})})}),e.jsxs("div",{className:"bg-white rounded-lg p-4 space-y-2 flex-1",children:[e.jsx("h4",{className:"text-sm font-medium text-gray-900 mb-2",children:"Breakdown"}),e.jsx("div",{className:"space-y-2",children:[...y].sort((u,F)=>F.stars-u.stars).map((u,F)=>{const W=y.length>0?u.value/y.reduce((H,ee)=>H+ee.value,0)*100:0;return e.jsxs("div",{className:"flex items-center space-x-2",children:[e.jsxs("div",{className:"flex items-center space-x-1 w-12",children:[e.jsx("span",{className:"text-xs font-medium text-gray-700",children:u.stars}),e.jsx(Me,{className:"h-3 w-3 text-yellow-400 fill-current"})]}),e.jsx("div",{className:"flex-1 bg-gray-200 rounded-full h-2 relative overflow-hidden",children:e.jsx("div",{className:"h-full rounded-full transition-all duration-500",style:{width:`${W}%`,backgroundColor:B[F%B.length]}})}),e.jsx("div",{className:"text-xs font-medium text-gray-900 w-8 text-right",children:u.value})]},u.stars)})}),e.jsxs("div",{className:"flex items-center justify-between border-t pt-2",children:[e.jsx("div",{className:"text-xs font-bold text-gray-500",children:"Total"}),e.jsx("div",{className:"text-xs font-medium text-gray-900",children:b})]})]})]}):e.jsx("div",{className:"h-64 flex items-center justify-center text-gray-500",children:"No rating data available"})]}),e.jsxs("div",{className:"bg-gray-50 rounded-lg p-6",children:[e.jsxs("h3",{className:"text-lg font-semibold text-gray-900 mb-4 flex items-center",children:[e.jsx(Ge,{className:"h-5 w-5 mr-2 text-blue-600"}),"Plugin Downloads"]}),X?e.jsx("div",{className:"h-64 flex items-center justify-center",children:e.jsx("div",{className:"animate-spin rounded-full h-8 w-8 border-b-2 border-blue-600"})}):Object.keys(i).length>0?e.jsxs("div",{className:"space-y-4",children:[D&&e.jsx("div",{className:"bg-white rounded-lg p-4 border border-green-200",children:e.jsxs("div",{className:"flex items-center justify-between",children:[e.jsxs("div",{className:"flex items-center space-x-3",children:[e.jsx("div",{className:"w-10 h-10 bg-green-100 rounded-lg flex items-center justify-center",children:e.jsx(Qe,{className:"h-5 w-5 text-green-600"})}),e.jsxs("div",{children:[e.jsxs("div",{className:"flex items-center space-x-2",children:[e.jsxs("span",{className:"font-semibold text-gray-900",children:["Version ",D]}),e.jsx("span",{className:"bg-green-100 text-green-800 text-xs font-medium px-2 py-1 rounded-full",children:"Current"})]}),e.jsx("div",{className:"text-sm text-gray-500",children:"Latest stable release"})]})]}),e.jsx("a",{href:i[D],target:"_blank",rel:"noopener noreferrer",className:"w-10 h-10 bg-green-600 hover:bg-green-700 text-white rounded-lg flex items-center justify-center transition-colors",title:"Download Current Version",children:e.jsx(xe,{className:"h-4 w-4"})})]})}),Object.keys(M).length>0&&e.jsx("div",{className:"bg-white rounded-lg p-4 border border-gray-200",children:e.jsxs("div",{className:"",children:[e.jsxs("div",{className:"flex items-center justify-between mb-2",children:[e.jsx("div",{className:"w-10 h-10 bg-gray-100 rounded-lg flex items-center justify-center",children:e.jsx(Qe,{className:"h-5 w-5 text-gray-600"})}),e.jsx("span",{className:"font-semibold text-gray-900",children:"Previous Versions"}),e.jsx("span",{className:"bg-gray-100 text-gray-800 text-xs font-medium px-2 py-1 rounded-full",children:"Archive"})]}),e.jsxs("div",{className:"flex gap-2",children:[e.jsxs("div",{className:"relative flex-1",ref:l,children:[e.jsxs("button",{type:"button",onClick:m,className:"w-full px-3 py-2 text-sm border border-gray-300 rounded-lg bg-white focus:outline-none focus:ring-2 focus:ring-blue-500 focus:border-transparent text-left flex items-center justify-between",children:[e.jsx("span",{className:R?"text-gray-900":"text-gray-500",children:R||"Select a version"}),v?e.jsx(Es,{className:"h-4 w-4 text-gray-400"}):e.jsx(Ls,{className:"h-4 w-4 text-gray-400"})]}),v&&e.jsxs("div",{className:"absolute z-10 w-full bottom-full mb-1 bg-white border border-gray-300 rounded-lg shadow-lg",children:[e.jsx("div",{className:"p-2 border-b border-gray-200",children:e.jsxs("div",{className:"relative",children:[e.jsx(je,{className:"absolute left-2 top-1/2 transform -translate-y-1/2 h-4 w-4 text-gray-400"}),e.jsx("input",{type:"text",placeholder:"Search versions...",value:h,onChange:u=>g(u.target.value),className:"w-full pl-8 pr-3 py-1.5 text-sm border border-gray-200 rounded focus:outline-none focus:ring-1 focus:ring-blue-500 focus:border-transparent",autoFocus:!0})]})}),e.jsx("div",{className:"max-h-48 overflow-y-auto",children:ce().length>0?ce().map(u=>e.jsx("button",{type:"button",onClick:()=>be(u),className:"w-full px-3 py-2 text-sm text-left hover:bg-gray-50 focus:bg-gray-50 focus:outline-none transition-colors",children:u},u)):e.jsx("div",{className:"px-3 py-2 text-sm text-gray-500 text-center",children:h.trim()?"No versions found":"No versions available"})})]})]}),e.jsx("div",{children:e.jsx("a",{href:R?M[R]:"#",target:R?"_blank":"_self",rel:"noopener noreferrer",className:`w-10 h-10 rounded-lg flex items-center justify-center transition-colors ml-3 ${R?"bg-gray-600 hover:bg-gray-700 text-white cursor-pointer":"bg-gray-300 text-gray-500 cursor-not-allowed"}`,title:R?"Download Selected Version":"Select a version first",onClick:R?void 0:u=>u.preventDefault(),children:e.jsx(xe,{className:"h-4 w-4"})})})]})]})})]}):e.jsx("div",{className:"h-64 flex items-center justify-center text-gray-500",children:"No version data available"})]})]})]})]})}):null},dt=s=>{if(!s||typeof s!="string")return!1;const n=s.split(".");if(n.length!==3)return!1;try{return n.forEach(a=>{if(a.length===0)throw new Error("Empty JWT part");atob(a.replace(/-/g,"+").replace(/_/g,"/"))}),!0}catch{return!1}},Ce=()=>{const s=localStorage.getItem("adminToken");return s?dt(s)?s:(console.warn("Invalid JWT token found, clearing localStorage"),localStorage.removeItem("adminToken"),localStorage.removeItem("adminUser"),null):null},es=(s,n)=>{var a;((n==null?void 0:n.status)===401||(a=s==null?void 0:s.message)!=null&&a.includes("token"))&&(console.warn("Authentication error detected, clearing tokens"),localStorage.removeItem("adminToken"),localStorage.removeItem("adminUser"),window.location.pathname!=="/login"&&(window.location.href="/login"))},mt=(s,n,a=!0,p="Auto Refresh")=>{const l=r.useRef(null),c=r.useRef(null),d=r.useCallback(()=>{if(!a||!s)return;const y=new Date,$=new Date(y.getTime()+6*60*60*1e3),b=$.getUTCHours(),f=$.getUTCMinutes(),L=$.getUTCDate(),[k,j]=n.split(":").map(Number),w=b===k&&f===j,i=c.current===L;if(w&&!i){console.log(`🕐 ${p} triggered at ${n} GMT+6`),c.current=L;try{s()}catch(x){console.error(`❌ ${p} failed:`,x)}}},[s,n,a,p]);r.useEffect(()=>{if(!a){l.current&&(clearInterval(l.current),l.current=null);return}return l.current=setInterval(d,6e4),d(),()=>{l.current&&(clearInterval(l.current),l.current=null)}},[d,a]),r.useEffect(()=>()=>{l.current&&clearInterval(l.current)},[])},ut=()=>e.jsxs("div",{className:"bg-white rounded-lg p-6 shadow-sm border border-gray-200 animate-pulse",children:[e.jsxs("div",{className:"flex items-start justify-between mb-4",children:[e.jsxs("div",{className:"flex items-center space-x-3",children:[e.jsx("div",{className:"w-12 h-12 bg-gray-200 rounded-lg"}),e.jsxs("div",{children:[e.jsx("div",{className:"h-4 bg-gray-200 rounded w-24 mb-2"}),e.jsx("div",{className:"h-3 bg-gray-200 rounded w-16"})]})]}),e.jsx("div",{className:"w-6 h-6 bg-gray-200 rounded"})]}),e.jsxs("div",{className:"grid grid-cols-2 gap-4 mb-4",children:[e.jsxs("div",{children:[e.jsx("div",{className:"h-3 bg-gray-200 rounded w-12 mb-1"}),e.jsx("div",{className:"h-6 bg-gray-200 rounded w-16"})]}),e.jsxs("div",{children:[e.jsx("div",{className:"h-3 bg-gray-200 rounded w-16 mb-1"}),e.jsx("div",{className:"h-4 bg-gray-200 rounded w-20"})]})]}),e.jsxs("div",{className:"grid grid-cols-2 gap-4 mb-4",children:[e.jsxs("div",{children:[e.jsx("div",{className:"h-3 bg-gray-200 rounded w-14 mb-1"}),e.jsx("div",{className:"h-4 bg-gray-200 rounded w-18"})]}),e.jsxs("div",{children:[e.jsx("div",{className:"h-3 bg-gray-200 rounded w-12 mb-1"}),e.jsx("div",{className:"h-4 bg-gray-200 rounded w-16"})]})]}),e.jsxs("div",{className:"flex space-x-2",children:[e.jsx("div",{className:"h-8 bg-gray-200 rounded flex-1"}),e.jsx("div",{className:"h-8 bg-gray-200 rounded flex-1"}),e.jsx("div",{className:"h-8 bg-gray-200 rounded w-8"})]})]}),xt=({plugin:s,onRemove:n,onRefresh:a,canAddPlugins:p})=>{var f,L;const[l,c]=r.useState(!1),[d,y]=r.useState(!1),$=k=>{if(!k)return{formatted:"N/A",daysDiff:"N/A"};try{const j=k.match(/^(\d{4})-(\d{2})-(\d{2})/);if(!j)return{formatted:"N/A",daysDiff:"N/A"};const[,w,i,x]=j,D=`${x}-${i}-${w}`,P=new Date(`${w}-${i}-${x}`),M=new Date;if(isNaN(P.getTime()))return{formatted:"N/A",daysDiff:"N/A"};const U=M-P,R=Math.floor(U/(1e3*60*60*24));return{formatted:D,daysDiff:R}}catch{return{formatted:"N/A",daysDiff:"N/A"}}},b=async()=>{if(a){y(!0);try{await a(s.slug)}finally{y(!1)}}};return e.jsxs(e.Fragment,{children:[e.jsxs("div",{className:"bg-white border border-gray-200 rounded-lg p-6 hover:shadow-lg transition-all duration-200",children:[e.jsxs("div",{className:"flex items-start justify-between mb-4",children:[e.jsxs("div",{className:"flex items-center space-x-2 flex-1 overflow-hidden",children:[e.jsxs("div",{className:"w-12 h-12 border rounded-lg flex items-center justify-center overflow-hidden",children:[s.icons&&(s.icons["2x"]||s.icons["1x"])?e.jsx("img",{src:s.icons["2x"]||s.icons["1x"],alt:`${s.displayName} icon`,className:"w-full h-full object-cover rounded-lg",onError:k=>{k.target.style.display="none",k.target.nextSibling.style.display="flex"}}):null,e.jsx(rs,{className:`h-6 w-6 text-black ${s.icons&&(s.icons["2x"]||s.icons["1x"])?"hidden":""}`})]}),e.jsxs("div",{className:"flex-1 min-w-0",children:[e.jsx("h4",{className:"font-semibold text-gray-900 truncate text-lg",children:s.displayName}),e.jsx("p",{className:"text-sm text-gray-500 font-mono whitespace-nowrap",children:s.slug})]})]}),e.jsxs("div",{className:"flex items-center space-x-1",children:[e.jsx("button",{onClick:b,disabled:d,className:"text-gray-400 hover:text-green-500 transition-colors p-1 disabled:opacity-50",title:"Refresh plugin data",children:e.jsx(oe,{className:`h-4 w-4 ${d?"animate-spin":""}`})}),p&&e.jsx("button",{onClick:()=>n(s.slug),className:"text-gray-400 hover:text-red-500 transition-colors p-1",title:"Remove plugin",children:e.jsx($e,{className:"h-4 w-4"})})]})]}),e.jsxs("div",{className:"space-y-3 mb-4",children:[e.jsxs("div",{className:"flex items-center justify-between space-x-4",children:[e.jsxs("span",{className:"text-sm font-medium px-2 py-1 rounded-full bg-green-100 text-green-800",children:["v",s.version||"N/A"]}),e.jsxs("div",{className:"flex items-center space-x-1",children:[e.jsxs("span",{className:"text-sm font-medium text-gray-900",children:["#",s.currentRank||"N/A"]}),((f=s.rankHistory)==null?void 0:f.rankChange)!==null&&((L=s.rankHistory)==null?void 0:L.rankChange)!==void 0&&e.jsxs("span",{className:`text-xs ${s.rankHistory.rankChange>0?"text-green-600":s.rankHistory.rankChange<0?"text-red-600":"text-gray-600"}`,children:[s.rankHistory.rankChange>0?"↑":s.rankHistory.rankChange<0?"↓":"→",Math.abs(s.rankHistory.rankChange)]})]})]}),e.jsxs("div",{className:"flex items-center justify-between",children:[e.jsx("span",{className:"text-sm text-gray-600",children:"Released"}),e.jsx("div",{className:`text-sm font-medium px-2 py-1 rounded ${(()=>{const k=s.lastReleaseDate||s.lastFetched,j=$(k);return j.daysDiff==="N/A"||j.daysDiff<=20?"bg-gray-100 text-gray-700":"bg-yellow-50 text-yellow-700"})()}`,children:(()=>{const k=s.lastReleaseDate||s.lastFetched,j=$(k);return e.jsxs(e.Fragment,{children:[j.formatted,j.daysDiff!=="N/A"&&e.jsxs("span",{className:"text-xs ml-1",children:["(",j.daysDiff," days)"]})]})})()})]})]}),e.jsxs("div",{className:"mt-4 border border-gray-200 rounded-lg overflow-hidden",children:[e.jsx("div",{className:"bg-gray-50 px-3 py-2 border-b border-gray-200",children:e.jsxs("div",{className:"flex items-center justify-between",children:[e.jsx("h5",{className:"text-sm font-medium text-gray-900",children:"Download Trends"}),s.downloadTrend&&e.jsxs("span",{className:`text-xs px-2 py-1 rounded-full ${s.downloadTrend.isPositive?"bg-green-100 text-green-800":"bg-red-100 text-red-800"}`,children:[s.downloadTrend.isPositive?"↑":"↓"," ",s.downloadTrend.changePercent,"%"]})]})}),s.downloadTrend?e.jsxs("table",{className:"min-w-full divide-y divide-gray-200",children:[e.jsxs("tbody",{className:"bg-white divide-y divide-gray-200",children:[e.jsxs("tr",{className:"",children:[e.jsx("td",{className:"px-4 py-2 whitespace-nowrap text-sm font-medium text-gray-700",children:"Yesterday"}),e.jsx("td",{className:"px-4 py-2 whitespace-nowrap text-sm text-gray-700 font-bold text-right",children:s.downloadTrend.yesterdayDownloads.toLocaleString()})]}),e.jsxs("tr",{className:"",children:[e.jsx("td",{className:"px-4 py-2 whitespace-nowrap text-sm font-medium text-gray-700",children:"Day Before"}),e.jsx("td",{className:"px-4 py-2 whitespace-nowrap text-sm text-gray-900 font-bold text-right",children:s.downloadTrend.dayBeforeDownloads.toLocaleString()})]})]}),e.jsx("tfoot",{className:"bg-gray-100 border-t border-gray-200",children:e.jsxs("tr",{children:[e.jsx("td",{className:"px-4 py-2 text-sm font-semibold text-gray-900",children:"Changes"}),e.jsx("td",{className:"px-4 py-2 whitespace-nowrap text-right",children:e.jsxs("span",{className:`text-sm font-bold ${s.downloadTrend.change>=0?"text-green-600":"text-red-600"}`,children:[s.downloadTrend.change>=0?"+":"",s.downloadTrend.change.toLocaleString()]})})]})})]}):e.jsx("div",{className:"text-center py-4",children:e.jsx("span",{className:"text-xs text-gray-500",children:"No download trend data available"})})]}),e.jsx("div",{className:"pt-4 border-t border-gray-100",children:e.jsxs("button",{onClick:()=>c(!0),className:"w-full bg-gradient-to-r from-blue-600 to-purple-600 text-white py-2 px-4 rounded-lg font-medium hover:from-blue-700 hover:to-purple-700 transition-all duration-200 flex items-center justify-center space-x-2",children:[e.jsx(_s,{className:"h-4 w-4"}),e.jsx("span",{children:"View Chart & Analytics"})]})})]}),e.jsx(ct,{isOpen:l,onClose:()=>c(!1),plugin:s})]})},ht=()=>{var F,W,H,ee;const{user:s,autoLogout:n}=we(),[a,p]=r.useState(!1),[l,c]=r.useState(""),[d,y]=r.useState(!1),[$,b]=r.useState(!1),[f,L]=r.useState(null),[k,j]=r.useState([]),[w,i]=r.useState(!0),[x,D]=r.useState(null),[P,M]=r.useState(!1),[U,R]=r.useState(!1),[S,h]=r.useState(null),[g,v]=r.useState(!1),[T,_]=r.useState(!1),[E,I]=r.useState(null),[O,V]=r.useState(""),[G,X]=r.useState(!1),te=s&&["admin","superadmin"].includes(s.role),ie=async()=>{try{const o=Ce();if(!o){console.warn("No valid authentication token found for database check"),n("No valid authentication token found. Please login again.");return}const N=await fetch("https://pluginsight.vercel.app/api/plugins/check-database",{headers:{Authorization:`Bearer ${o}`,"Content-Type":"application/json"}});if(N.ok){const C=await N.json();C.success&&b(C.hasPlugins)}else if(N.status===401){console.warn("Authentication failed during database check");let C="Your session has expired. Please login again.";try{const q=await N.json();q.message&&(C=q.message)}catch{}n(C)}else es(null,N)}catch(o){if(console.error("Error checking database status:",o),o.message&&(o.message.includes("expired")||o.message.includes("token")||o.message.includes("Authentication failed"))){console.warn("JWT token error detected during database check:",o.message),n(o.message);return}es(o)}},he=async()=>{try{y(!0),L({current:0,total:0,page:0,totalPages:0,successCount:0,errorCount:0,percentComplete:0,estimatedTimeRemaining:null,averageTimePerPage:null,pluginsPerSecond:0,message:"Starting full plugin fetch (all 55,540+ plugins)..."});const o=Ce();if(!o){console.error("No valid authentication token found"),n("No valid authentication token found. Please login again.");return}const N=await fetch("https://pluginsight.vercel.app/api/plugins/fetch-all",{method:"POST",headers:{Authorization:`Bearer ${o}`,"Content-Type":"application/json"}});if(!N.ok){if(N.status===401){console.warn("Authentication failed during plugin fetch");let Y="Your session has expired. Please login again.";try{const se=await N.json();se.message&&(Y=se.message)}catch{}n(Y);return}throw new Error(`HTTP error! status: ${N.status}`)}const C=N.body.getReader(),q=new TextDecoder;for(;;){const{done:Y,value:se}=await C.read();if(Y)break;const ke=q.decode(se).split(`
`).filter(de=>de.trim());for(const de of ke)try{const t=JSON.parse(de);if(t.type==="progress")L({current:t.current||0,total:t.total||0,page:t.page||0,totalPages:t.totalPages||0,successCount:t.successCount||0,errorCount:t.errorCount||0,percentComplete:t.percentComplete||0,estimatedTimeRemaining:t.estimatedTimeRemaining,averageTimePerPage:t.averageTimePerPage,pluginsPerSecond:t.pluginsPerSecond||0,message:t.message||"Processing..."});else if(t.type==="complete")L({current:t.summary.totalProcessedPlugins,total:t.summary.totalPlugins,page:t.summary.totalPages,totalPages:t.summary.totalPages,successCount:t.summary.successfulPages,errorCount:t.summary.failedPages,percentComplete:100,averageTimePerPage:t.summary.averageTimePerPage,pluginsPerSecond:t.summary.averagePluginsPerSecond,successRate:t.summary.successRate,totalDuration:t.summary.totalDuration,message:`✅ Fetch completed! ${t.summary.totalProcessedPlugins.toLocaleString()} plugins processed in ${Math.round(t.summary.totalDuration/1e3/60)} minutes`}),t.errors&&t.errors.length>0&&console.warn("Some errors occurred during fetch:",t.errors),window.toast(`Successfully fetched ${t.summary.totalProcessedPlugins.toLocaleString()} plugins!`,"success");else if(t.type==="error")throw new Error(t.message||"Fetch failed")}catch(t){console.warn("Failed to parse streaming data:",t)}}}catch(o){if(console.error("Fetch all plugins error:",o),o.message&&(o.message.includes("expired")||o.message.includes("token")||o.message.includes("Authentication failed"))){console.warn("JWT token error detected during plugin fetch:",o.message),n(o.message);return}L({current:0,total:0,message:`❌ Full fetch failed: ${o.message}`,error:!0}),window.toast(`Fetch failed: ${o.message}`,"error")}finally{y(!1),setTimeout(()=>L(null),1e4),ie()}},ne=async()=>{i(!0);try{const o=await Ie("/api/plugins/added");if(!o.ok){let N=`HTTP error! status: ${o.status}`;try{const C=await o.json();C.message&&(N=C.message)}catch{}throw new Error(N)}const A=await o.json();if(A.success){const N=A.addedPlugins.map(C=>{var q;return{slug:C.pluginSlug,name:C.pluginName,displayName:C.displayName,currentRank:C.currentRank,rankGrowth:((q=C.rankHistory)==null?void 0:q.rankChange)||0,lastFetched:C.lastUpdated,short_description:C.short_description,version:C.version,lastReleaseDate:C.lastReleaseDate,icons:C.icons||{},rating:C.rating,numRatings:C.numRatings||0,currentVersion:C.currentVersion,previousVersions:C.previousVersions||[],rankHistory:C.rankHistory,downloadTrend:C.downloadTrend,downloadDataHistory:C.downloadDataHistory||[],reviewStats:C.reviewStats,versionInfo:C.versionInfo,pluginInformation:C.pluginInformation}});j(N)}else console.warn("Failed to load added plugins:",A.message)}catch(o){console.error("Error loading added plugins:",o),o.name==="TypeError"&&o.message.includes("Failed to fetch")?(console.warn("Unable to connect to server - backend may be down"),j([])):(console.warn("Failed to load added plugins:",o.message),j([]))}finally{i(!1)}},Ne=o=>{try{localStorage.setItem("pluginData",JSON.stringify(o))}catch(A){console.error("Error storing plugin data in localStorage:",A)}},ge=()=>{try{const o=localStorage.getItem("pluginData");return o?JSON.parse(o):null}catch(o){return console.error("Error retrieving plugin data from localStorage:",o),null}},pe=()=>{localStorage.removeItem("pluginData")},me=async()=>{if(!l.trim()){window.toast("Please enter a plugin slug","warning");return}try{M(!0);const o=`https://api.wordpress.org/plugins/info/1.2/?action=plugin_information&request[slug]=${l.trim()}&request[fields][icons]=true`,A=await fetch(o);if(!A.ok)throw new Error(`WordPress API error: ${A.status}`);const N=await A.json();if(N.error){window.toast(`Plugin not found: ${N.error}`,"error"),D(null);return}const C={slug:N.slug,name:N.name,version:N.version,author:N.author,rating:N.rating,active_installs:N.active_installs,num_ratings:N.num_ratings,downloaded:N.downloaded,last_updated:N.last_updated,short_description:N.short_description,homepage:N.homepage,requires:N.requires,tested:N.tested,requires_php:N.requires_php,icons:N.icons?{"1x":N.icons["1x"],"2x":N.icons["2x"]}:{},tags:N.tags?Object.keys(N.tags).slice(0,10).reduce((q,Y)=>(q[Y]=N.tags[Y],q),{}):{}};Ne(C),D({slug:N.slug,name:N.name,version:N.version,author:N.author,rating:N.rating,active_installs:N.active_installs,num_ratings:N.num_ratings,downloaded:N.downloaded,last_updated:N.last_updated,homepage:N.homepage,requires:N.requires,tested:N.tested,requires_php:N.requires_php}),window.toast("Plugin data fetched successfully from WordPress API","success")}catch(o){console.error("Error fetching plugin data:",o),window.toast("Failed to fetch plugin data from WordPress API","error"),D(null)}finally{M(!1)}};r.useEffect(()=>{ne(),ie()},[]);const le=async()=>{const o=ge();if(!o){window.toast("Please fetch plugin data first by clicking the Fetch button","warning");return}if(!l.trim()){window.toast("Please enter a plugin slug","warning");return}v(!0);try{const A=Ce();if(!A){console.error("No valid authentication token found"),n("No valid authentication token found. Please login again.");return}const C=await fetch("https://pluginsight.vercel.app/api/plugins/added-with-data",{method:"POST",headers:{Authorization:`Bearer ${A}`,"Content-Type":"application/json"},body:JSON.stringify({slug:l.trim(),pluginData:o})});if(!C.ok){if(C.status===401){console.warn("Authentication failed during plugin addition");let Y="Your session has expired. Please login again.";try{const se=await C.json();se.message&&(Y=se.message)}catch{}n(Y);return}else if(C.status===413){console.warn("Payload too large error during plugin addition");try{const Y=await C.json();window.toast(Y.message||"Plugin data is too large. Please try again or contact support.","error")}catch{window.toast("Plugin data is too large. Please try again or contact support.","error")}return}}const q=await C.json();q.success?(window.toast(q.message,"success"),pe(),c(""),p(!1),D(null),await ne()):window.toast(q.message||"Failed to add plugin","error")}catch(A){if(console.error("Add plugin error:",A),A.message&&(A.message.includes("expired")||A.message.includes("token")||A.message.includes("Authentication failed"))){console.warn("JWT token error detected during plugin addition:",A.message),n(A.message);return}window.toast("Failed to add plugin. Please try again.","error")}finally{v(!1)}},ce=o=>{const A=k.find(N=>N.slug===o)||addedPluginsListData.find(N=>N.slug===o);h(A),R(!0)},be=async()=>{if(S)try{const o=Ce();if(!o){console.error("No valid authentication token found"),n("No valid authentication token found. Please login again.");return}const N=await fetch(`https://pluginsight.vercel.app/api/plugins/added/${S.slug}`,{method:"DELETE",headers:{Authorization:`Bearer ${o}`,"Content-Type":"application/json"}});if(!N.ok&&N.status===401){console.warn("Authentication failed during plugin removal");let q="Your session has expired. Please login again.";try{const Y=await N.json();Y.message&&(q=Y.message)}catch{}n(q);return}const C=await N.json();C.success?(window.toast("Plugin removed successfully","success"),await ne()):window.toast(C.message||"Failed to remove plugin","error")}catch(o){if(console.error("Remove plugin error:",o),o.message&&(o.message.includes("expired")||o.message.includes("token")||o.message.includes("Authentication failed"))){console.warn("JWT token error detected during plugin removal:",o.message),n(o.message);return}window.toast("Failed to remove plugin","error")}finally{R(!1),h(null)}},m=async o=>{try{const A=Ce();if(!A){console.error("No valid authentication token found"),n("No valid authentication token found. Please login again.");return}const C=await fetch(`https://pluginsight.vercel.app/api/plugins/added/${o}/refresh`,{method:"POST",headers:{Authorization:`Bearer ${A}`,"Content-Type":"application/json"}});if(!C.ok&&C.status===401){console.warn("Authentication failed during plugin refresh");let Y="Your session has expired. Please login again.";try{const se=await C.json();se.message&&(Y=se.message)}catch{}n(Y);return}const q=await C.json();q.success?(window.toast(q.message,"success"),await ne()):window.toast(q.message||"Failed to refresh plugin","error")}catch(A){if(console.error("Refresh plugin error:",A),A.message&&(A.message.includes("expired")||A.message.includes("token")||A.message.includes("Authentication failed"))){console.warn("JWT token error detected during plugin refresh:",A.message),n(A.message);return}window.toast("Failed to refresh plugin","error")}},B=async()=>{try{_(!0),I({current:0,total:0,currentPlugin:null});const o=await Ie("/api/plugins/added");if(!o.ok)throw new Error(`Failed to fetch plugins: ${o.status}`);const A=await o.json();if(!A.success||!A.addedPlugins||A.addedPlugins.length===0){window.toast("No plugins found to refresh","warning");return}const N=A.addedPlugins.map(C=>C.slug);I({current:0,total:N.length,currentPlugin:null}),window.toast(`Starting refresh of ${N.length} plugins...`,"info");for(const C of N){const q=N.indexOf(C)+1;I({current:q,total:N.length,currentPlugin:C});try{const se=await(await Ie(`/api/plugins/added/${C}/refresh`,{method:"POST"})).json();se.success?console.log(`Successfully refreshed plugin: ${C}`):console.warn(`Failed to refresh plugin ${C}: ${se.message}`)}catch(Y){console.error(`Error refreshing plugin ${C}:`,Y)}}window.toast("All plugins refreshed successfully!","success"),await ne()}catch(o){if(console.error("Refresh all plugins error:",o),o.message&&(o.message.includes("expired")||o.message.includes("token")||o.message.includes("Authentication failed"))){console.warn("JWT token error detected during refresh all:",o.message),n(o.message);return}window.toast("Failed to refresh all plugins","error")}finally{_(!1),I(null)}};mt(async()=>{try{X(!0),console.log("🕐 Auto-refresh triggered at 9:00 AM GMT+6"),window.toast("Auto-refreshing all plugins...","info"),await B()}catch(o){console.error("Auto-refresh failed:",o),window.toast("Auto-refresh failed","error")}finally{X(!1)}},"09:00",!0,"Dashboard Plugin Refresh");const u=k.filter(o=>{var N,C;if(!O.trim())return!0;const A=O.toLowerCase();return((N=o.name)==null?void 0:N.toLowerCase().includes(A))||((C=o.slug)==null?void 0:C.toLowerCase().includes(A))}).sort((o,A)=>{const N=o.name||o.slug||"",C=A.name||A.slug||"";return N.toLowerCase().localeCompare(C.toLowerCase())});return e.jsxs("div",{className:"space-y-6",children:[e.jsxs("div",{className:"bg-white rounded-lg p-6 shadow-sm border border-gray-200",children:[e.jsxs("div",{className:"flex justify-between items-center",children:[e.jsxs("div",{children:[e.jsx("h2",{className:"text-2xl font-semibold text-gray-900",children:"Welcome to Admin Dashboard"}),e.jsx("p",{className:"text-sm text-gray-600",children:"Manage your plugins, analyze keywords, and track performance all in one place."})]}),e.jsxs("div",{className:"flex gap-4",children:[te&&e.jsx("button",{onClick:he,disabled:d,className:`flex items-center px-4 py-2 rounded-lg transition-colors ${d?"bg-gray-400 cursor-not-allowed":"bg-blue-600 hover:bg-blue-700"} text-white`,title:"Fetch all 55,540+ plugins from WordPress repository",children:d?e.jsxs(e.Fragment,{children:[e.jsx("div",{className:"animate-spin rounded-full h-4 w-4 border-b-2 border-white mr-2"}),"Fetching All..."]}):e.jsxs(e.Fragment,{children:[e.jsx(xe,{className:"h-4 w-4 mr-2"}),$?"Refetch":"Fetch"]})}),te&&e.jsxs("button",{onClick:()=>p(!0),disabled:d,className:`flex items-center px-4 py-2 rounded-lg transition-colors ${d?"bg-gray-400 cursor-not-allowed":"bg-green-600 hover:bg-green-700"} text-white`,children:[e.jsx(Se,{className:"h-4 w-4 mr-2"}),"Add Plugin"]})]})]}),f&&e.jsxs("div",{className:`mt-4 p-4 rounded-lg border ${f.error?"bg-red-50 border-red-200":"bg-blue-50 border-blue-200"}`,children:[e.jsxs("div",{className:"flex items-center justify-between mb-2",children:[e.jsx("span",{className:`text-sm font-medium ${f.error?"text-red-900":"text-blue-900"}`,children:f.message}),f.total>0&&e.jsxs("span",{className:`text-sm ${f.error?"text-red-700":"text-blue-700"}`,children:[(F=f.current)==null?void 0:F.toLocaleString(),"/",(W=f.total)==null?void 0:W.toLocaleString()]})]}),f.page&&f.totalPages&&e.jsxs("div",{className:"grid grid-cols-2 gap-4 mb-3 text-xs text-blue-700",children:[e.jsxs("div",{className:"space-y-1",children:[e.jsxs("div",{children:["Page: ",f.page,"/",f.totalPages]}),e.jsxs("div",{children:["Progress: ",f.percentComplete||0,"%"]})]}),e.jsxs("div",{className:"space-y-1",children:[e.jsxs("div",{children:["✅ ",(H=f.successCount)==null?void 0:H.toLocaleString()," success"]}),f.errorCount>0&&e.jsxs("div",{className:"text-red-600",children:["❌ ",(ee=f.errorCount)==null?void 0:ee.toLocaleString()," errors"]})]})]}),(f.pluginsPerSecond>0||f.estimatedTimeRemaining)&&e.jsxs("div",{className:"grid grid-cols-2 gap-4 mb-3 text-xs text-blue-600",children:[f.pluginsPerSecond>0&&e.jsxs("div",{children:["Speed: ",f.pluginsPerSecond," plugins/sec"]}),f.estimatedTimeRemaining&&e.jsxs("div",{children:["ETA:"," ",Math.round(f.estimatedTimeRemaining/1e3/60)," ","min"]}),f.averageTimePerPage&&e.jsxs("div",{children:["Avg: ",Math.round(f.averageTimePerPage/1e3),"s/page"]}),f.successRate&&e.jsxs("div",{children:["Success Rate: ",f.successRate,"%"]})]}),f.totalDuration&&e.jsx("div",{className:"mb-3 text-xs text-green-700 bg-green-50 p-2 rounded",children:e.jsxs("div",{className:"grid grid-cols-2 gap-2",children:[e.jsxs("div",{children:["Duration:"," ",Math.round(f.totalDuration/1e3/60)," ","minutes"]}),e.jsxs("div",{children:["Avg Speed: ",f.pluginsPerSecond," plugins/sec"]}),e.jsxs("div",{children:["Success Rate: ",f.successRate,"%"]}),e.jsxs("div",{children:["Pages: ",f.successCount,"/",f.totalPages]})]})}),f.total>0&&!f.error&&e.jsx("div",{className:"w-full bg-blue-200 rounded-full h-3",children:e.jsx("div",{className:"bg-blue-600 h-3 rounded-full transition-all duration-300 flex items-center justify-center",style:{width:`${Math.max(2,f.percentComplete||f.current/f.total*100)}%`},children:e.jsxs("span",{className:"text-xs text-white font-medium",children:[f.percentComplete||Math.round(f.current/f.total*100),"%"]})})})]})]}),e.jsxs("div",{className:"bg-white rounded-lg p-6 shadow-sm border border-gray-200",children:[e.jsxs("div",{className:"flex items-center justify-between mb-6",children:[e.jsxs("div",{children:[e.jsx("h3",{className:"text-xl font-semibold text-gray-900",children:"Added Plugins"}),e.jsx("div",{className:"text-sm text-gray-500",children:w?"Loading plugins...":k.length>0?`Showing ${u.length} of ${k.length} plugins`:"No plugins added yet"})]}),e.jsxs("div",{className:"flex items-center space-x-4",children:[!w&&k.length>0&&e.jsxs("div",{className:"relative",children:[e.jsx(je,{className:"absolute left-3 top-1/2 transform -translate-y-1/2 h-4 w-4 text-gray-400"}),e.jsx("input",{type:"text",placeholder:"Search plugins...",value:O,onChange:o=>V(o.target.value),className:"pl-10 pr-4 py-2 border border-gray-300 rounded-lg text-sm focus:outline-none focus:ring-2 focus:ring-blue-500 focus:border-transparent w-64"})]}),te&&!w&&k.length>0&&e.jsx("button",{onClick:B,disabled:T||G,className:`flex items-center px-4 py-2 rounded-lg transition-colors ${T||G?"bg-gray-400 cursor-not-allowed":"bg-blue-600 hover:bg-blue-700"} text-white text-sm`,title:G?"Auto-refresh in progress...":"Refresh all plugins sequentially",children:T||G?e.jsxs(e.Fragment,{children:[e.jsx("div",{className:"animate-spin rounded-full h-4 w-4 border-b-2 border-white mr-2"}),G?"Auto-refreshing...":"Refreshing..."]}):e.jsxs(e.Fragment,{children:[e.jsx(oe,{className:"h-4 w-4 mr-2"}),"Refresh All"]})})]})]}),E&&e.jsxs("div",{className:"mb-6 p-4 rounded-lg border bg-blue-50 border-blue-200",children:[e.jsxs("div",{className:"flex items-center justify-between mb-2",children:[e.jsx("span",{className:"text-sm font-medium text-blue-900",children:"Refreshing plugins..."}),e.jsxs("span",{className:"text-sm text-blue-700",children:[E.current,"/",E.total]})]}),E.currentPlugin&&e.jsxs("div",{className:"text-sm text-blue-700 mb-2",children:["Currently refreshing:"," ",e.jsx("span",{className:"font-mono",children:E.currentPlugin})]}),e.jsx("div",{className:"w-full bg-blue-200 rounded-full h-2",children:e.jsx("div",{className:"bg-blue-600 h-2 rounded-full transition-all duration-300",style:{width:`${Math.max(2,E.current/E.total*100)}%`}})}),e.jsx("div",{className:"text-xs text-blue-600 mt-2",children:"Note: Each plugin refresh has a 60-second delay to avoid overwhelming the backend."})]}),w?e.jsx("div",{className:"grid grid-cols-1 md:grid-cols-2 lg:grid-cols-4 gap-6",children:Array.from({length:8}).map((o,A)=>e.jsx(ut,{},A))}):k.length>0?u.length>0?e.jsx("div",{className:"grid grid-cols-1 md:grid-cols-2 lg:grid-cols-4 gap-6",children:u.map(o=>e.jsx(xt,{plugin:o,onRemove:ce,onRefresh:m,canAddPlugins:te},o.slug))}):e.jsxs("div",{className:"text-center py-12",children:[e.jsx("div",{className:"w-16 h-16 bg-gray-100 rounded-full flex items-center justify-center mx-auto mb-4",children:e.jsx(je,{className:"h-8 w-8 text-gray-400"})}),e.jsx("h4",{className:"text-lg font-medium text-gray-900 mb-2",children:"No plugins found"}),e.jsxs("p",{className:"text-gray-600",children:['No plugins match your search query "',O,'". Try adjusting your search terms.']})]}):e.jsxs("div",{className:"text-center py-12",children:[e.jsx("div",{className:"w-16 h-16 bg-gray-100 rounded-full flex items-center justify-center mx-auto mb-4",children:e.jsx(rs,{className:"h-8 w-8 text-gray-400"})}),e.jsx("h4",{className:"text-lg font-medium text-gray-900 mb-2",children:"No plugins added yet"}),e.jsx("p",{className:"text-gray-600",children:'Start tracking your WordPress plugins by adding them to your dashboard using the "Add Plugin" button above.'})]})]}),e.jsx(ye,{isOpen:a,onClose:()=>{g||(p(!1),D(null),c(""),pe())},title:"Add New Plugin",children:e.jsxs("div",{className:"space-y-4",children:[e.jsxs("div",{children:[e.jsx("label",{htmlFor:"pluginSlug",className:"block text-sm font-medium text-gray-700 mb-2",children:"Plugin Slug"}),e.jsxs("div",{className:"flex space-x-2",children:[e.jsx("input",{id:"pluginSlug",type:"text",value:l,onChange:o=>c(o.target.value),className:"flex-1 px-3 py-2 border border-gray-300 rounded-lg focus:outline-none focus:ring-2 focus:ring-blue-500 focus:border-transparent",placeholder:"e.g., my-awesome-plugin"}),e.jsx("button",{onClick:me,disabled:P||!l.trim(),className:"px-4 py-2 bg-green-600 text-white rounded-lg hover:bg-green-700 transition-colors disabled:opacity-50 disabled:cursor-not-allowed flex items-center space-x-2",children:P?e.jsxs(e.Fragment,{children:[e.jsx("div",{className:"animate-spin rounded-full h-4 w-4 border-b-2 border-white"}),e.jsx("span",{children:"Fetching..."})]}):e.jsxs(e.Fragment,{children:[e.jsx(xe,{className:"h-4 w-4"}),e.jsx("span",{children:"Fetch"})]})})]})]}),x&&e.jsxs("div",{className:"bg-green-50 border border-green-200 rounded-lg p-4 space-y-3",children:[e.jsx("h4",{className:"font-semibold text-green-900",children:"Plugin Information (Fetched from WordPress API)"}),e.jsxs("div",{className:"grid grid-cols-2 gap-4 text-sm",children:[e.jsxs("div",{children:[e.jsx("span",{className:"text-green-700",children:"Name:"}),e.jsx("span",{className:"ml-2 font-medium text-green-900",children:x.name})]}),e.jsxs("div",{children:[e.jsx("span",{className:"text-green-700",children:"Version:"}),e.jsx("span",{className:"ml-2 font-medium text-green-900",children:x.version||"N/A"})]}),e.jsxs("div",{children:[e.jsx("span",{className:"text-green-700",children:"Author:"}),e.jsx("span",{className:"ml-2 font-medium text-green-900",children:(()=>{const o=x.author;if(!o)return"N/A";const A=o.match(/<a[^>]*>(.*?)<\/a>/);return A?A[1]:o})()})]}),e.jsxs("div",{children:[e.jsx("span",{className:"text-green-700",children:"Rating:"}),e.jsx("span",{className:"ml-2 font-medium text-green-900",children:x.rating?`${x.rating}/100`:"N/A"})]}),e.jsxs("div",{children:[e.jsx("span",{className:"text-green-700",children:"Active Installs:"}),e.jsx("span",{className:"ml-2 font-medium text-green-900",children:x.active_installs?x.active_installs.toLocaleString():"N/A"})]}),e.jsxs("div",{children:[e.jsx("span",{className:"text-green-700",children:"Last Updated:"}),e.jsx("span",{className:"ml-2 font-medium text-green-900",children:x.last_updated||"N/A"})]}),e.jsxs("div",{className:"col-span-2",children:[e.jsx("span",{className:"text-green-700",children:"WordPress Requirements:"}),e.jsxs("span",{className:"ml-2 font-medium text-green-900",children:["WP ",x.requires||"N/A"," | Tested up to"," ",x.tested||"N/A"," | PHP"," ",x.requires_php||"N/A"]})]})]})]}),!x&&e.jsx("div",{className:"bg-blue-50 border border-blue-200 rounded-lg p-4",children:e.jsxs("p",{className:"text-blue-800 text-sm",children:[e.jsx("strong",{children:"Step 1:"}),' Enter a plugin slug and click "Fetch" to retrieve plugin information from WordPress API.',e.jsx("br",{}),e.jsx("strong",{children:"Step 2:"}),' Once plugin data is displayed, click "Add Plugin" to add it to your dashboard.']})}),e.jsxs("div",{className:"flex justify-end space-x-3 pt-4",children:[e.jsx("button",{onClick:()=>{p(!1),D(null),c(""),pe()},disabled:g,className:"px-4 py-2 text-gray-600 hover:text-gray-800 transition-colors disabled:opacity-50 disabled:cursor-not-allowed",children:"Cancel"}),e.jsx("button",{onClick:le,disabled:g||!x,className:"px-4 py-2 bg-blue-600 text-white rounded-lg hover:bg-blue-700 transition-colors disabled:opacity-50 disabled:cursor-not-allowed flex items-center space-x-2",children:g?e.jsxs(e.Fragment,{children:[e.jsx("div",{className:"animate-spin rounded-full h-4 w-4 border-b-2 border-white"}),e.jsx("span",{children:"Adding..."})]}):e.jsx("span",{children:"Add Plugin"})})]})]})}),e.jsx(ye,{isOpen:U,onClose:()=>{R(!1),h(null)},title:"Confirm Delete",children:e.jsxs("div",{className:"space-y-4",children:[e.jsxs("div",{className:"flex items-center space-x-3",children:[e.jsx("div",{className:"w-12 h-12 bg-red-100 rounded-full flex items-center justify-center",children:e.jsx($e,{className:"h-6 w-6 text-red-600"})}),e.jsxs("div",{children:[e.jsx("h4",{className:"text-lg font-semibold text-gray-900",children:"Delete Plugin"}),e.jsxs("p",{className:"text-gray-600",children:['Are you sure you want to remove "',(S==null?void 0:S.displayName)||(S==null?void 0:S.name),'" from your added plugins?']})]})]}),e.jsx("div",{className:"bg-yellow-50 border border-yellow-200 rounded-lg p-3",children:e.jsxs("p",{className:"text-sm text-yellow-800",children:[e.jsx("strong",{children:"Warning:"})," This action cannot be undone. The plugin will be removed from your dashboard and you'll need to add it again if you want to track it."]})}),e.jsxs("div",{className:"flex justify-end space-x-3 pt-4",children:[e.jsx("button",{onClick:()=>{R(!1),h(null)},className:"px-4 py-2 text-gray-600 hover:text-gray-800 transition-colors",children:"Cancel"}),e.jsxs("button",{onClick:be,className:"px-4 py-2 bg-red-600 text-white rounded-lg hover:bg-red-700 transition-colors flex items-center space-x-2",children:[e.jsx($e,{className:"h-4 w-4"}),e.jsx("span",{children:"Delete Plugin"})]})]})]})})]})},gt=()=>{var U,R,S;const[s,n]=r.useState([]),[a,p]=r.useState(""),[l,c]=r.useState("7"),[d,y]=r.useState({start:"",end:""}),[$,b]=r.useState([]),[f,L]=r.useState(!1),[k,j]=r.useState(!1);console.log("Chart data: ",$);const w=h=>{var V;const{cx:g,cy:v,payload:T}=h;if(!a||!T)return null;const _=((V=s.find(G=>G.pluginSlug===a))==null?void 0:V.displayName)||a,E=T[`${_}_trend`],I=T[_];if(I==null)return null;let O="#3B82F6";return E==="improvement"?O="#10B981":E==="decline"&&(O="#EF4444"),e.jsx("circle",{cx:g,cy:v,r:5,fill:O,stroke:O,strokeWidth:2})},i=async()=>{var h;try{const g=localStorage.getItem("adminToken"),T=await fetch("https://pluginsight.vercel.app/api/plugins/rank/all?limit=1000",{headers:{Authorization:`Bearer ${g}`,"Content-Type":"application/json"}});if(!T.ok)throw new Error(`HTTP error! status: ${T.status}`);const _=await T.json();_.success?(n(_.plugins),console.log(`✅ Loaded ${((h=_.plugins)==null?void 0:h.length)||0} plugins from plugininformations collection (${_.pluginsWithRankHistory||0} with rank history)`)):console.warn("Failed to load plugins:",_.message)}catch(g){console.error("Error loading plugins from plugininformations collection:",g),g.name==="TypeError"&&g.message.includes("Failed to fetch")?window.toast("Unable to connect to server. Please check if the backend is running.","error"):window.toast("Failed to load plugins from database","error")}},x=async()=>{if(!a){b([]);return}try{L(!0);const h=s.find(v=>v.pluginSlug===a);if(!h){console.error("Selected plugin not found in loaded plugins"),window.toast("Selected plugin not found","error"),b([]);return}if(!h.rankHistory||!Array.isArray(h.rankHistory)){console.log(`Plugin ${a} has no rank history data`),b([]);return}console.log(`📊 Processing rank history for ${a}: ${h.rankHistory.length} entries`);const g=D(h);b(g)}catch(h){console.error("Error loading chart data:",h),window.toast("Failed to load chart data","error")}finally{L(!1)}},D=h=>{if(!h.rankHistory||!Array.isArray(h.rankHistory))return[];const g=h.displayName||h.pluginName||h.pluginSlug;let v=h.rankHistory;if(l!=="custom"){const E=parseInt(l),I=new Date;I.setDate(I.getDate()-E),v=h.rankHistory.filter(O=>{const[V,G,X]=O.date.split("-");return new Date(X,G-1,V)>=I})}else if(d.start&&d.end){const E=new Date(d.start),I=new Date(d.end);v=h.rankHistory.filter(O=>{const[V,G,X]=O.date.split("-"),te=new Date(X,G-1,V);return te>=E&&te<=I})}return v.sort((E,I)=>{const[O,V,G]=E.date.split("-"),[X,te,ie]=I.date.split("-"),he=new Date(G,V-1,O),ne=new Date(ie,te-1,X);return he-ne}).map((E,I,O)=>{const V=O[I-1];let G="stable";return V&&(E.previousRank<V.previousRank?G="improvement":E.previousRank>V.previousRank&&(G="decline")),{date:E.date,[g]:E.previousRank,[`${g}_trend`]:G}})},P=async()=>{if(!a){window.toast("Please select a plugin first","warning");return}try{L(!0),window.toast("Refreshing chart data...","info"),await x(),window.toast("Chart data refreshed successfully","success")}catch(h){console.error("Error refreshing chart data:",h),window.toast("Failed to refresh chart data","error")}finally{L(!1)}};r.useEffect(()=>{i()},[]),r.useEffect(()=>{x()},[a,l,d]);const M=h=>{c(h),j(h==="custom")};return e.jsxs("div",{className:"space-y-6",children:[e.jsx("div",{className:"p-4",children:e.jsx("div",{className:"flex items-center justify-between",children:e.jsxs("div",{children:[e.jsxs("h1",{className:"text-2xl font-bold text-gray-900 flex items-center",children:[e.jsx(ve,{className:"h-8 w-8 text-blue-600 mr-3"}),"Plugin Rank Analysis"]}),e.jsx("p",{className:"text-sm text-gray-600",children:"Track ranking trends for your added plugins"})]})})}),e.jsx("div",{className:"bg-white rounded-lg p-4 shadow-sm border border-gray-200",children:e.jsxs("div",{className:"flex flex-wrap items-center justify-between gap-4",children:[e.jsxs("div",{className:"flex items-center space-x-4",children:[e.jsxs("div",{children:[e.jsx("label",{className:"block text-sm font-medium text-gray-700 mb-1",children:"Plugin"}),e.jsxs("select",{value:a,onChange:h=>p(h.target.value),className:"w-full px-3 py-2 border border-gray-300 rounded-md text-sm focus:outline-none focus:ring-2 focus:ring-blue-500 focus:border-blue-500",children:[e.jsx("option",{value:"",children:"Choose a plugin"}),s.map(h=>e.jsx("option",{value:h.pluginSlug,children:h.displayName},h.pluginSlug))]})]}),e.jsxs("div",{children:[e.jsx("label",{className:"block text-sm font-medium text-gray-700 mb-1",children:"Date Range"}),e.jsxs("select",{value:l,onChange:h=>M(h.target.value),className:"w-full px-3 py-2 border border-gray-300 rounded-md text-sm focus:outline-none focus:ring-2 focus:ring-blue-500 focus:border-blue-500",children:[e.jsx("option",{value:"",children:"Select date"}),e.jsx("option",{value:"7",children:"Last 7 days"}),e.jsx("option",{value:"15",children:"Last 15 days"}),e.jsx("option",{value:"30",children:"Last 30 days"}),e.jsx("option",{value:"custom",children:"Custom range"})]})]}),k&&e.jsxs("div",{className:"flex gap-2",children:[e.jsxs("div",{children:[e.jsx("label",{className:"block text-xs font-medium text-gray-700 mb-1",children:"Start Date"}),e.jsx("input",{type:"date",value:d.start,onChange:h=>y(g=>({...g,start:h.target.value})),className:"px-3 py-2 border border-gray-300 rounded-md text-sm focus:outline-none focus:ring-2 focus:ring-blue-500"})]}),e.jsxs("div",{children:[e.jsx("label",{className:"block text-xs font-medium text-gray-700 mb-1",children:"End Date"}),e.jsx("input",{type:"date",value:d.end,onChange:h=>y(g=>({...g,end:h.target.value})),className:"px-3 py-2 border border-gray-300 rounded-md text-sm focus:outline-none focus:ring-2 focus:ring-blue-500"})]})]})]}),e.jsx("div",{className:"flex gap-2",children:e.jsxs("button",{onClick:P,disabled:f||!a,className:"flex items-center px-4 py-2 bg-blue-600 text-white rounded-lg hover:bg-blue-700 transition-colors disabled:opacity-50 disabled:cursor-not-allowed",title:"Fetch latest rank from WordPress and save to database",children:[e.jsx(oe,{className:`h-4 w-4 mr-2 ${f?"animate-spin":""}`}),"Refresh"]})})]})}),e.jsxs("div",{className:"bg-white rounded-lg p-6 shadow-sm border border-gray-200",children:[e.jsxs("h3",{className:"text-lg font-semibold text-gray-900 mb-6",children:["Plugin Rank Trends",a&&e.jsxs("span",{className:"text-sm font-normal text-gray-500 ml-2",children:["(",((U=s.find(h=>h.pluginSlug===a))==null?void 0:U.displayName)||a,")"]})]}),f?e.jsx("div",{className:"flex items-center justify-center h-96",children:e.jsxs("div",{className:"text-center",children:[e.jsx(oe,{className:"h-8 w-8 text-blue-600 animate-spin mx-auto mb-3"}),e.jsx("p",{className:"text-gray-600",children:"Loading chart data..."})]})}):$.length>0?e.jsx("div",{className:"h-96",children:e.jsx(Ae,{width:"100%",height:"100%",children:e.jsxs(Je,{data:$,children:[e.jsx(Te,{strokeDasharray:"3 3"}),e.jsx(Ee,{dataKey:"date",tick:{fontSize:12},angle:-45,textAnchor:"end",height:60}),e.jsx(Le,{tick:{fontSize:12},domain:(()=>{var G;if($.length===0)return[1,100];const h=((G=s.find(X=>X.pluginSlug===a))==null?void 0:G.displayName)||a,g=$.map(X=>X[h]).filter(X=>X!=null);if(g.length===0)return[1,100];const v=Math.min(...g),T=Math.max(...g),_=g[g.length-1],E=Math.max(1,_-10),I=_+10,O=Math.min(E,v-2),V=Math.max(I,T+2);return[O,V]})(),reversed:!0,label:{value:"Rank",angle:-90,position:"insideLeft"},allowDecimals:!1,type:"number"}),e.jsx(Pe,{labelFormatter:h=>`Date: ${h}`,formatter:(h,g)=>[h?`#${h}`:"No data",g]}),e.jsx(Ws,{}),a&&e.jsx(Ye,{type:"monotone",dataKey:((R=s.find(h=>h.pluginSlug===a))==null?void 0:R.displayName)||a,stroke:"#3B82F6",strokeWidth:2,dot:e.jsx(w,{}),connectNulls:!1,activeDot:{r:6,stroke:"#3B82F6",strokeWidth:2},children:e.jsx(_e,{dataKey:((S=s.find(h=>h.pluginSlug===a))==null?void 0:S.displayName)||a,position:"top",fontSize:10,fill:"#374151",formatter:h=>h?`#${h}`:""})},a)]})})}):e.jsx("div",{className:"flex items-center justify-center h-96",children:e.jsxs("div",{className:"text-center",children:[e.jsx(ve,{className:"h-16 w-16 text-gray-300 mx-auto mb-4"}),e.jsx("h4",{className:"text-lg font-medium text-gray-900 mb-2",children:"No Data Available"}),e.jsx("p",{className:"text-gray-600 mb-4",children:a?"No rank history found for the selected plugin and date range":"Select a plugin to view its rank trends"}),s.length===0&&e.jsx("p",{className:"text-sm text-gray-500",children:"Add plugins from the Dashboard first to see their ranking trends."})]})})]})]})},hs=s=>{const n=document.createElement("textarea");return n.innerHTML=s,n.value},pt=(s,n=40)=>{const a=hs(s);if(a.length<=n)return a;const p=a.split(" ");let l=p[0];for(let c=1;c<p.length&&(l+" "+p[c]).length<=n-3;c++)l+=" "+p[c];return l+"..."},ft=()=>{var de;const[s,n]=r.useState("performance"),[a,p]=r.useState([]),[l,c]=r.useState(""),[d,y]=r.useState([]),[$,b]=r.useState(!1),[f,L]=r.useState(!1),[k,j]=r.useState(""),[w,i]=r.useState(""),[x,D]=r.useState(!1),[P,M]=r.useState(new Set),[U,R]=r.useState(!1),[S,h]=r.useState(new Set),[g,v]=r.useState(null),[T,_]=r.useState(!1),[E,I]=r.useState([]),[O,V]=r.useState(!1),[G,X]=r.useState(!1),[te,ie]=r.useState(""),[he,ne]=r.useState({}),[Ne,ge]=r.useState(!1),[pe,me]=r.useState(!1),[le,ce]=r.useState(null),[be,m]=r.useState([]),[B,Z]=r.useState([]),[u,F]=r.useState(!1),[W,H]=r.useState(!1),ee=async()=>{var t;try{const z=localStorage.getItem("adminToken"),K=await(await fetch("https://pluginsight.vercel.app/api/plugins/rank/all?limit=1000",{headers:{Authorization:`Bearer ${z}`,"Content-Type":"application/json"}})).json();K.success?(p(K.plugins),console.log(`✅ Loaded ${((t=K.plugins)==null?void 0:t.length)||0} plugins from plugininformations collection for keyword analysis`)):console.error("Failed to load plugins:",K.message)}catch(z){console.error("Error loading plugins from plugininformations collection:",z),window.toast("Failed to load plugins from database","error")}},o=async()=>{try{if(b(!0),!l){y([]),ne({}),b(!1);return}const t=localStorage.getItem("adminToken"),J=`https://pluginsight.vercel.app/api/keywords?pluginSlug=${l}`,K=await(await fetch(J,{headers:{Authorization:`Bearer ${t}`,"Content-Type":"application/json"}})).json();if(K.success){const ae=K.keywords.map(fe=>({...fe,position:fe.latestRank,lastChecked:fe.lastChecked||fe.updatedAt}));y(ae);const re={};ae.forEach(fe=>{re[fe._id]=fe.occurrences||0}),ne(re)}else console.error("Failed to load keywords:",K.message),window.toast(K.message||"Failed to load keywords","error"),y([]),ne({})}catch(t){console.error("Error loading keywords:",t),window.toast("Failed to load keywords","error"),y([]),ne({})}finally{b(!1)}},A=async()=>{if(!w||!k.trim()){window.toast("Please select a plugin and enter a keyword","error");return}try{const t=localStorage.getItem("adminToken"),z=a.find(ae=>ae.pluginSlug===w),K=await(await fetch("https://pluginsight.vercel.app/api/keywords",{method:"POST",headers:{Authorization:`Bearer ${t}`,"Content-Type":"application/json"},body:JSON.stringify({pluginSlug:w,pluginName:(z==null?void 0:z.displayName)||w,keyword:k.trim()})})).json();K.success?(window.toast("Keyword added successfully","success"),j(""),i(""),L(!1),w===l&&o()):window.toast(K.message||"Failed to add keyword","error")}catch(t){console.error("Error adding keyword:",t),window.toast("Failed to add keyword","error")}},N=async()=>{try{D(!0),window.toast("Refreshing keyword ranks...","info");const t=localStorage.getItem("adminToken"),Q=await(await fetch("https://pluginsight.vercel.app/api/keywords/refresh-ranks",{method:"POST",headers:{Authorization:`Bearer ${t}`,"Content-Type":"application/json"}})).json();Q.success?(window.toast(Q.message,"success"),await o()):window.toast(Q.message||"Failed to refresh keyword ranks","error")}catch(t){console.error("Error refreshing keyword ranks:",t),window.toast("Failed to refresh keyword ranks","error")}finally{D(!1)}},C=async()=>{try{const t=localStorage.getItem("adminToken"),z="https://pluginsight.vercel.app",J=Array.from(P),K=await(await fetch(`${z}/api/keywords/bulk-delete`,{method:"DELETE",headers:{Authorization:`Bearer ${t}`,"Content-Type":"application/json"},body:JSON.stringify({keywordIds:J})})).json();K.success?(window.toast(`${J.length} keywords deleted successfully`,"success"),M(new Set),R(!1),o()):window.toast(K.message||"Failed to delete keywords","error")}catch(t){console.error("Error deleting keywords:",t),window.toast("Failed to delete keywords","error")}},q=async()=>{if(g)try{const t=localStorage.getItem("adminToken"),Q=await(await fetch(`https://pluginsight.vercel.app/api/keywords/${g}`,{method:"DELETE",headers:{Authorization:`Bearer ${t}`,"Content-Type":"application/json"}})).json();if(Q.success){window.toast("Keyword deleted successfully","success"),_(!1),v(null);const K=new Set(S);K.delete(g),h(K),o()}else window.toast(Q.message||"Failed to delete keyword","error")}catch(t){console.error("Error deleting keyword:",t),window.toast("Failed to delete keyword","error")}},Y=async(t=!1)=>{try{V(!0);const z=localStorage.getItem("adminToken"),ae=await(await fetch(`https://pluginsight.vercel.app/api/competitors${t?"?autoDiscover=true":""}`,{headers:{Authorization:`Bearer ${z}`,"Content-Type":"application/json"}})).json();ae.success&&(I(ae.competitors),t&&ae.competitors.length>0&&window.toast(`Discovered ${ae.competitors.length} competitor plugins`,"success"))}catch(z){console.error("Error loading competitors:",z),window.toast("Failed to load competitors","error")}finally{V(!1)}},se=async()=>{if(!te.trim()){window.toast("Please enter a plugin slug","error");return}try{const t=localStorage.getItem("adminToken"),Q=await(await fetch("https://pluginsight.vercel.app/api/competitors",{method:"POST",headers:{Authorization:`Bearer ${t}`,"Content-Type":"application/json"},body:JSON.stringify({pluginSlug:te.trim()})})).json();Q.success?(window.toast("Competitor added successfully","success"),ie(""),X(!1),Y()):window.toast(Q.message||"Failed to add competitor","error")}catch(t){console.error("Error adding competitor:",t),window.toast("Failed to add competitor","error")}},Ue=async t=>{ce(t),ge(!0),F(!0);try{const z=localStorage.getItem("adminToken"),K=await(await fetch(`https://pluginsight.vercel.app/api/keywords/ranks/${encodeURIComponent(t.keyword)}/${t.pluginSlug}?limit=30`,{headers:{Authorization:`Bearer ${z}`,"Content-Type":"application/json"}})).json();K.success?m(K.rankHistory):(window.toast("Failed to load rank history","error"),m([]))}catch(z){console.error("Error loading rank history:",z),window.toast("Failed to load rank history","error"),m([])}finally{F(!1)}},ke=async t=>{ce(t),me(!0),H(!0);try{const J=`https://api.wordpress.org/plugins/info/1.2/?action=query_plugins&request[search]=${encodeURIComponent(t.keyword)}&request[per_page]=50&request[fields][active_installs]=true&request[fields][ratings]=true&request[fields][tested]=true&request[fields][last_updated]=true`,K=await(await fetch(J)).json();if(K&&K.plugins){const ae=K.plugins.filter(re=>re.slug!==t.pluginSlug).sort((re,fe)=>(fe.active_installs||0)-(re.active_installs||0)).slice(0,10).map(re=>({pluginName:re.name,pluginSlug:re.slug,activeInstalls:re.active_installs||0,rating:re.rating||0,numRatings:re.num_ratings||0,testedUpTo:re.tested||"N/A",lastUpdated:re.last_updated||"N/A",wordpressUrl:`https://wordpress.org/plugins/${re.slug}/`}));Z(ae)}else window.toast("Failed to load related plugins","error"),Z([])}catch(z){console.error("Error loading related plugins:",z),window.toast("Failed to load related plugins","error"),Z([])}finally{H(!1)}};return r.useEffect(()=>{ee()},[]),r.useEffect(()=>{o()},[l]),r.useEffect(()=>{s==="competitors"&&Y(!0)},[s]),e.jsxs("div",{className:"space-y-6",children:[e.jsxs("div",{className:"p-4",children:[e.jsx("div",{className:"flex flex-col md:flex-row md:items-center justify-between gap-4 mb-6",children:e.jsx("div",{children:e.jsxs("h1",{className:"text-2xl font-bold text-gray-900 flex items-center",children:[e.jsx(je,{className:"h-6 w-6 text-blue-600 mr-2"}),"Keyword Analysis"]})})}),e.jsx("div",{className:"border-b border-gray-200",children:e.jsxs("nav",{className:"-mb-px flex space-x-8",children:[e.jsxs("button",{onClick:()=>n("performance"),className:`py-2 px-1 border-b-2 font-medium text-sm ${s==="performance"?"border-blue-500 text-blue-600":"border-transparent text-gray-500 hover:text-gray-700 hover:border-gray-300"}`,children:[e.jsx(ve,{className:"h-4 w-4 inline mr-2"}),"Keyword Performance"]}),e.jsxs("button",{onClick:()=>n("competitors"),className:`py-2 px-1 border-b-2 font-medium text-sm ${s==="competitors"?"border-blue-500 text-blue-600":"border-transparent text-gray-500 hover:text-gray-700 hover:border-gray-300"}`,children:[e.jsx(Re,{className:"h-4 w-4 inline mr-2"}),"Competitors"]})]})})]}),s==="performance"&&e.jsxs(e.Fragment,{children:[e.jsx("div",{className:"bg-white rounded-lg p-4 shadow-sm border border-gray-200",children:e.jsxs("div",{className:"flex justify-between items-stretch md:items-center gap-3",children:[e.jsxs("div",{className:"flex items-center gap-2",children:[e.jsx("label",{className:"text-sm font-medium text-gray-700 whitespace-nowrap",children:"Plugin:"}),e.jsxs("select",{value:l,onChange:t=>c(t.target.value),className:"px-3 py-2 border border-gray-300 rounded-md text-sm focus:outline-none focus:ring-2 focus:ring-blue-500 focus:border-blue-500",children:[e.jsx("option",{value:"",children:"All plugins"}),a.map(t=>e.jsx("option",{value:t.pluginSlug,children:t.displayName},t.pluginSlug))]})]}),e.jsxs("div",{className:"flex items-center gap-2",children:[P.size>0&&e.jsxs("button",{onClick:()=>R(!0),className:"flex items-center px-3 py-2 bg-red-600 text-white rounded-md hover:bg-red-700 transition-colors text-sm",children:[e.jsx($e,{className:"h-4 w-4 mr-1"}),"Delete (",P.size,")"]}),e.jsxs("button",{onClick:()=>L(!0),className:"flex items-center px-3 py-2 bg-blue-600 text-white rounded-md hover:bg-blue-700 transition-colors text-sm",children:[e.jsx(Se,{className:"h-4 w-4 mr-1"}),"Add"]}),e.jsxs("button",{onClick:N,disabled:x,className:"flex items-center px-3 py-2 bg-green-600 text-white rounded-md hover:bg-green-700 transition-colors disabled:opacity-50 text-sm",children:[e.jsx(oe,{className:`h-4 w-4 mr-1 ${x?"animate-spin":""}`}),x?"Refreshing...":"Refresh Ranks"]})]})]})}),e.jsxs("div",{className:"bg-white rounded-lg shadow-sm border border-gray-200",children:[e.jsxs("div",{className:"px-4 py-3 border-b border-gray-200 flex justify-between items-center",children:[e.jsxs("h3",{className:"text-base font-semibold text-gray-900",children:["Keywords",l&&e.jsxs("span",{className:"text-sm font-normal text-gray-500 ml-2",children:["for"," ",(de=a.find(t=>t.pluginSlug===l))==null?void 0:de.displayName]})]}),e.jsx("div",{className:"flex justify-between items-center",children:e.jsxs("div",{className:"text-sm text-gray-700",children:["Showing ",d.length," keywords"]})})]}),$?e.jsxs("div",{className:"p-8 text-center",children:[e.jsx(oe,{className:"h-8 w-8 text-blue-600 animate-spin mx-auto mb-3"}),e.jsx("p",{className:"text-gray-600",children:"Loading keywords..."})]}):d.length>0?e.jsx(e.Fragment,{children:e.jsx("div",{className:"overflow-x-auto max-h-[470px] overflow-y-auto",children:e.jsxs("table",{className:"min-w-full divide-y divide-gray-200",children:[e.jsx("thead",{className:"bg-gray-50 sticky top-0 z-10",children:e.jsxs("tr",{children:[e.jsx("th",{className:"px-4 py-2 text-left text-xs font-medium text-gray-500 uppercase tracking-wider w-12",children:e.jsx("input",{type:"checkbox",checked:d.every(t=>P.has(t._id))&&d.length>0,onChange:t=>{const z=new Set(P);t.target.checked?d.forEach(J=>z.add(J._id)):d.forEach(J=>z.delete(J._id)),M(z)},className:"rounded border-gray-300 text-blue-600 focus:ring-blue-500"})}),e.jsx("th",{className:"px-4 py-2 text-left text-xs font-medium text-gray-500 uppercase tracking-wider w-1/4",children:"Keyword"}),e.jsx("th",{className:"px-4 py-2 text-left text-xs font-medium text-gray-500 uppercase tracking-wider w-1/6",children:"Position"}),e.jsx("th",{className:"px-4 py-2 text-left text-xs font-medium text-gray-500 uppercase tracking-wider w-20",children:"Analytics"}),e.jsx("th",{className:"px-4 py-2 text-left text-xs font-medium text-gray-500 uppercase tracking-wider w-20",children:"Occurrences"}),e.jsx("th",{className:"px-4 py-2 text-left text-xs font-medium text-gray-500 uppercase tracking-wider w-1/6",children:"Tracked"}),e.jsx("th",{className:"px-4 py-2 text-left text-xs font-medium text-gray-500 uppercase tracking-wider w-1/6",children:"Updated"})]})}),e.jsx("tbody",{className:"bg-white divide-y divide-gray-200",children:d.map((t,z)=>e.jsxs("tr",{className:z%2===0?"bg-white":"bg-gray-50",children:[e.jsx("td",{className:"px-4 py-3 whitespace-nowrap w-12",children:e.jsx("input",{type:"checkbox",checked:P.has(t._id),onChange:J=>{const Q=new Set(P);J.target.checked?Q.add(t._id):Q.delete(t._id),M(Q)},className:"rounded border-gray-300 text-blue-600 focus:ring-blue-500"})}),e.jsx("td",{className:"px-4 py-3 whitespace-nowrap w-1/4",children:e.jsxs("div",{className:"flex items-center space-x-2",children:[e.jsx("div",{children:e.jsx("div",{className:"text-sm font-medium text-gray-900",children:t.keyword})}),t.source&&e.jsx("span",{className:`inline-flex items-center px-2 py-1 rounded-full text-xs font-medium ${t.source==="manual"?"bg-blue-100 text-blue-800":"bg-green-100 text-green-800"}`,children:t.source})]})}),e.jsx("td",{className:"px-4 py-3 whitespace-nowrap text-sm text-gray-600 w-1/6",children:e.jsxs("div",{className:"flex items-center space-x-2",children:[e.jsx("span",{children:t.position||"-"}),t.rankChange!==null&&t.rankChange!==void 0&&e.jsxs("span",{className:`inline-flex items-center px-1.5 py-0.5 rounded-full text-xs font-medium ${t.rankChange<0?"bg-green-100 text-green-800":t.rankChange>0?"bg-red-100 text-red-800":"bg-gray-100 text-gray-800"}`,children:[t.rankChange<0?"↑":t.rankChange>0?"↓":"=",Math.abs(t.rankChange)]})]})}),e.jsx("td",{className:"px-4 py-3 whitespace-nowrap text-sm w-20",children:e.jsxs("div",{className:"flex items-center space-x-2",children:[e.jsx("button",{onClick:()=>Ue(t),className:"inline-flex items-center p-1.5 bg-blue-100 text-blue-600 rounded-md hover:bg-blue-200 transition-colors",title:"View Rank Analytics",children:e.jsx(ve,{className:"h-4 w-4"})}),e.jsx("button",{onClick:()=>ke(t),className:"inline-flex items-center p-1.5 bg-green-100 text-green-600 rounded-md hover:bg-green-200 transition-colors",title:"Search Related Plugins",children:e.jsx(Us,{className:"h-4 w-4"})})]})}),e.jsx("td",{className:"px-4 py-3 whitespace-nowrap text-sm text-gray-600 w-20",children:he[t._id]||0}),e.jsx("td",{className:"px-4 py-3 whitespace-nowrap text-sm text-gray-600 w-1/6",children:t.addedAt?new Date(t.addedAt).toLocaleDateString("en-GB"):"-"}),e.jsx("td",{className:"px-4 py-3 whitespace-nowrap text-sm text-gray-600 w-1/6",children:t.updatedAt?new Date(t.updatedAt).toLocaleDateString("en-GB"):"-"})]},t._id))})]})})}):e.jsxs("div",{className:"p-8 text-center",children:[e.jsx(je,{className:"h-12 w-12 text-gray-300 mx-auto mb-4"}),e.jsx("h4",{className:"text-lg font-medium text-gray-900 mb-2",children:"No Keywords Found"}),e.jsx("p",{className:"text-gray-600 mb-4",children:l?"No keywords added for this plugin yet.":"Please select a plugin first to view and manage keywords."}),l&&e.jsxs("button",{onClick:()=>L(!0),className:"inline-flex items-center px-4 py-2 bg-blue-600 text-white rounded-lg hover:bg-blue-700 transition-colors",children:[e.jsx(Se,{className:"h-4 w-4 mr-2"}),"Add First Keyword"]})]})]})]}),s==="competitors"&&e.jsxs("div",{className:"bg-white rounded-lg shadow-sm border border-gray-200",children:[e.jsxs("div",{className:"px-4 py-3 border-b border-gray-200 flex justify-between items-center",children:[e.jsx("h3",{className:"text-base font-semibold text-gray-900",children:"Competitor Plugins"}),e.jsxs("div",{className:"flex items-center gap-2",children:[e.jsxs("button",{onClick:()=>Y(!0),disabled:O,className:"flex items-center px-3 py-2 bg-green-600 text-white rounded-md hover:bg-green-700 transition-colors disabled:opacity-50 text-sm",children:[e.jsx(oe,{className:`h-4 w-4 mr-1 ${O?"animate-spin":""}`}),O?"Discovering...":"Discover"]}),e.jsxs("button",{onClick:()=>X(!0),className:"flex items-center px-3 py-2 bg-blue-600 text-white rounded-md hover:bg-blue-700 transition-colors text-sm",children:[e.jsx(Se,{className:"h-4 w-4 mr-1"}),"Add Manually"]})]})]}),O?e.jsxs("div",{className:"p-8 text-center",children:[e.jsx(oe,{className:"h-8 w-8 text-blue-600 animate-spin mx-auto mb-3"}),e.jsx("p",{className:"text-gray-600",children:"Loading competitors..."})]}):E.length>0?e.jsx("div",{className:"overflow-x-auto",children:e.jsxs("table",{className:"min-w-full divide-y divide-gray-200",children:[e.jsx("thead",{className:"bg-gray-50",children:e.jsxs("tr",{children:[e.jsx("th",{className:"px-4 py-2 text-left text-xs font-medium text-gray-500 uppercase tracking-wider",children:"Plugin Name"}),e.jsx("th",{className:"px-4 py-2 text-left text-xs font-medium text-gray-500 uppercase tracking-wider",children:"Slug"}),e.jsx("th",{className:"px-4 py-2 text-left text-xs font-medium text-gray-500 uppercase tracking-wider",children:"Current Rank"}),e.jsx("th",{className:"px-4 py-2 text-left text-xs font-medium text-gray-500 uppercase tracking-wider",children:"Active Installs"}),e.jsx("th",{className:"px-4 py-2 text-left text-xs font-medium text-gray-500 uppercase tracking-wider",children:"Tags"}),e.jsx("th",{className:"px-4 py-2 text-left text-xs font-medium text-gray-500 uppercase tracking-wider",children:"Added Date"})]})}),e.jsx("tbody",{className:"bg-white divide-y divide-gray-200",children:E.map((t,z)=>{var J;return e.jsxs("tr",{className:z%2===0?"bg-white":"bg-gray-50",children:[e.jsx("td",{className:"px-4 py-3 whitespace-nowrap",children:e.jsx("div",{className:"text-sm font-medium text-gray-900",children:t.pluginName})}),e.jsx("td",{className:"px-4 py-3 whitespace-nowrap",children:e.jsx("div",{className:"text-xs text-gray-500 font-mono",children:t.pluginSlug})}),e.jsx("td",{className:"px-4 py-3 whitespace-nowrap text-sm text-gray-600",children:t.currentRank?`#${t.currentRank}`:"N/A"}),e.jsx("td",{className:"px-4 py-3 whitespace-nowrap text-sm text-gray-600",children:((J=t.activeInstalls)==null?void 0:J.toLocaleString())||"N/A"}),e.jsx("td",{className:"px-4 py-3 whitespace-nowrap text-sm text-gray-600",children:e.jsxs("div",{className:"flex flex-wrap gap-1",children:[t.tags&&t.tags.length>0?t.tags.slice(0,3).map((Q,K)=>e.jsx("span",{className:"inline-flex items-center px-2 py-1 rounded-full text-xs bg-blue-100 text-blue-800",children:Q},`${t._id}-tag-${K}`)):e.jsx("span",{className:"text-gray-400",children:"No tags"}),t.tags&&t.tags.length>3&&e.jsxs("span",{className:"text-xs text-gray-500",children:["+",t.tags.length-3," more"]})]})}),e.jsx("td",{className:"px-4 py-3 whitespace-nowrap text-sm text-gray-600",children:new Date(t.createdAt).toLocaleDateString("en-GB")})]},t._id)})})]})}):e.jsxs("div",{className:"p-8 text-center",children:[e.jsx(Re,{className:"h-12 w-12 text-gray-300 mx-auto mb-4"}),e.jsx("h4",{className:"text-lg font-medium text-gray-900 mb-2",children:"No Competitors Found"}),e.jsx("p",{className:"text-gray-600 mb-4",children:"Add keywords to automatically discover competitor plugins, or add competitors manually."}),e.jsxs("button",{onClick:()=>X(!0),className:"inline-flex items-center px-4 py-2 bg-blue-600 text-white rounded-lg hover:bg-blue-700 transition-colors",children:[e.jsx(Se,{className:"h-4 w-4 mr-2"}),"Add First Competitor"]})]})]}),e.jsx(ye,{isOpen:f,onClose:()=>{L(!1),j(""),i("")},title:"Add New Keyword",children:e.jsxs("div",{className:"space-y-4",children:[e.jsxs("div",{children:[e.jsx("label",{className:"block text-sm font-medium text-gray-700 mb-2",children:"Select Plugin"}),e.jsxs("select",{value:w,onChange:t=>i(t.target.value),className:"w-full px-3 py-2 border border-gray-300 rounded-lg focus:outline-none focus:ring-2 focus:ring-blue-500 focus:border-transparent",children:[e.jsx("option",{value:"",children:"Choose a plugin..."}),a.map(t=>e.jsx("option",{value:t.pluginSlug,children:t.displayName},t.pluginSlug))]})]}),e.jsxs("div",{children:[e.jsx("label",{className:"block text-sm font-medium text-gray-700 mb-2",children:"Keyword"}),e.jsx("input",{type:"text",value:k,onChange:t=>j(t.target.value),className:"w-full px-3 py-2 border border-gray-300 rounded-lg focus:outline-none focus:ring-2 focus:ring-blue-500 focus:border-transparent",placeholder:"Enter keyword...",onKeyDown:t=>{t.key==="Enter"&&A()}})]}),e.jsxs("div",{className:"flex justify-end space-x-3 pt-4",children:[e.jsx("button",{onClick:()=>{L(!1),j(""),i("")},className:"px-4 py-2 text-gray-600 hover:text-gray-800 transition-colors",children:"Cancel"}),e.jsx("button",{onClick:A,className:"px-4 py-2 bg-blue-600 text-white rounded-lg hover:bg-blue-700 transition-colors",children:"Add Keyword"})]})]})}),e.jsx(ye,{isOpen:U,onClose:()=>R(!1),title:"Delete Keywords",children:e.jsxs("div",{className:"space-y-4",children:[e.jsxs("p",{className:"text-gray-600",children:["Are you sure you want to delete ",P.size," selected keyword(s)? This action cannot be undone and will also remove all related analytics data."]}),e.jsxs("div",{className:"flex justify-end space-x-3 pt-4",children:[e.jsx("button",{onClick:()=>R(!1),className:"px-4 py-2 text-gray-600 hover:text-gray-800 transition-colors",children:"Cancel"}),e.jsx("button",{onClick:C,className:"px-4 py-2 bg-red-600 text-white rounded-lg hover:bg-red-700 transition-colors",children:"Delete Keywords"})]})]})}),e.jsx(ye,{isOpen:T,onClose:()=>{_(!1),v(null)},title:"Delete Keyword",children:e.jsxs("div",{className:"space-y-4",children:[e.jsx("p",{className:"text-gray-600",children:"Are you sure you want to delete this keyword? This action cannot be undone and will also remove all related analytics data."}),e.jsxs("div",{className:"flex justify-end space-x-3 pt-4",children:[e.jsx("button",{onClick:()=>{_(!1),v(null)},className:"px-4 py-2 text-gray-600 hover:text-gray-800 transition-colors",children:"Cancel"}),e.jsx("button",{onClick:q,className:"px-4 py-2 bg-red-600 text-white rounded-lg hover:bg-red-700 transition-colors",children:"Delete Keyword"})]})]})}),e.jsx(ye,{isOpen:G,onClose:()=>{X(!1),ie("")},title:"Add Competitor Plugin",children:e.jsxs("div",{className:"space-y-4",children:[e.jsxs("div",{children:[e.jsx("label",{className:"block text-sm font-medium text-gray-700 mb-2",children:"Plugin Slug"}),e.jsx("input",{type:"text",value:te,onChange:t=>ie(t.target.value),className:"w-full px-3 py-2 border border-gray-300 rounded-lg focus:outline-none focus:ring-2 focus:ring-blue-500 focus:border-transparent",placeholder:"Enter plugin slug...",onKeyDown:t=>{t.key==="Enter"&&se()}})]}),e.jsxs("div",{className:"flex justify-end space-x-3 pt-4",children:[e.jsx("button",{onClick:()=>{X(!1),ie("")},className:"px-4 py-2 text-gray-600 hover:text-gray-800 transition-colors",children:"Cancel"}),e.jsx("button",{onClick:se,className:"px-4 py-2 bg-blue-600 text-white rounded-lg hover:bg-blue-700 transition-colors",children:"Add Competitor"})]})]})}),e.jsx(ye,{isOpen:Ne,onClose:()=>{ge(!1),ce(null),m([])},title:`Rank Analytics - ${(le==null?void 0:le.keyword)||""}`,children:e.jsxs("div",{className:"space-y-4",children:[u?e.jsxs("div",{className:"p-8 text-center",children:[e.jsx(oe,{className:"h-8 w-8 text-blue-600 animate-spin mx-auto mb-3"}),e.jsx("p",{className:"text-gray-600",children:"Loading rank history..."})]}):be.length>0?e.jsx("div",{className:"space-y-4",children:e.jsxs("div",{className:"bg-white rounded-lg border border-gray-200 p-4",children:[e.jsxs("div",{className:"flex justify-between items-center mb-4",children:[e.jsx("h4",{className:"text-sm font-medium text-gray-900",children:"Rank Trend (Last 30 days)"}),e.jsxs("p",{className:"text-sm text-gray-600",children:[e.jsx("strong",{children:"Plugin:"})," ",le==null?void 0:le.pluginName]})]}),e.jsx("div",{className:"h-64",children:e.jsx(Ae,{width:"100%",height:"100%",children:e.jsxs(Je,{data:be.slice().reverse().map(t=>({...t,displayDate:t.date,invertedRank:t.rank?-t.rank:0})),margin:{top:5,right:30,left:20,bottom:5},children:[e.jsx(Te,{strokeDasharray:"3 3",stroke:"#f0f0f0"}),e.jsx(Ee,{dataKey:"displayDate",tick:{fontSize:12},tickLine:{stroke:"#d1d5db"},axisLine:{stroke:"#d1d5db"}}),e.jsx(Le,{domain:["dataMin","dataMax"],tick:{fontSize:12},tickLine:{stroke:"#d1d5db"},axisLine:{stroke:"#d1d5db"},tickFormatter:t=>`#${Math.abs(t)}`}),e.jsx(Pe,{contentStyle:{backgroundColor:"#ffffff",border:"1px solid #d1d5db",borderRadius:"6px",fontSize:"12px"},formatter:t=>[`#${Math.abs(t)}`,"Rank"],labelFormatter:t=>`Date: ${t}`}),e.jsx(Ye,{type:"monotone",dataKey:"invertedRank",stroke:"#3b82f6",strokeWidth:2,dot:{fill:"#3b82f6",strokeWidth:2,r:4},activeDot:{r:6,stroke:"#3b82f6",strokeWidth:2},children:e.jsx(_e,{dataKey:"invertedRank",position:"top",formatter:t=>`#${Math.abs(t)}`,style:{fontSize:"12px",fill:"#374151",fontWeight:"500"}})})]})})})]})}):e.jsxs("div",{className:"p-8 text-center",children:[e.jsx(Fs,{className:"h-12 w-12 text-gray-300 mx-auto mb-4"}),e.jsx("h4",{className:"text-lg font-medium text-gray-900 mb-2",children:"No Rank History"}),e.jsx("p",{className:"text-gray-600",children:"No rank history found for this keyword."})]}),e.jsx("div",{className:"flex justify-end pt-4",children:e.jsx("button",{onClick:()=>{ge(!1),ce(null),m([])},className:"px-4 py-2 bg-gray-600 text-white rounded-lg hover:bg-gray-700 transition-colors",children:"Close"})})]})}),e.jsx(ye,{isOpen:pe,onClose:()=>{me(!1),ce(null),Z([])},title:`Related Plugins - "${(le==null?void 0:le.keyword)||""}"`,maxWidth:"max-w-6xl",fixedHeight:!0,children:e.jsxs("div",{className:"flex flex-col h-full",children:[e.jsx("div",{className:"flex-1 overflow-y-auto",children:W?e.jsxs("div",{className:"p-8 text-center",children:[e.jsx(oe,{className:"h-8 w-8 text-blue-600 animate-spin mx-auto mb-3"}),e.jsx("p",{className:"text-gray-600",children:"Loading related plugins..."})]}):B.length>0?e.jsx("div",{className:"overflow-x-auto",children:e.jsxs("table",{className:"min-w-full divide-y divide-gray-200",children:[e.jsx("thead",{className:"bg-gray-50 sticky top-0",children:e.jsxs("tr",{children:[e.jsx("th",{className:"px-4 py-2 text-left text-xs font-medium text-gray-500 uppercase tracking-wider",children:"Plugin Name"}),e.jsx("th",{className:"px-4 py-2 text-left text-xs font-medium text-gray-500 uppercase tracking-wider",children:"Active Installs"}),e.jsx("th",{className:"px-4 py-2 text-left text-xs font-medium text-gray-500 uppercase tracking-wider",children:"Rating"}),e.jsx("th",{className:"px-4 py-2 text-left text-xs font-medium text-gray-500 uppercase tracking-wider",children:"Tested Up To"}),e.jsx("th",{className:"px-4 py-2 text-left text-xs font-medium text-gray-500 uppercase tracking-wider",children:"Last Update"})]})}),e.jsx("tbody",{className:"bg-white divide-y divide-gray-200",children:B.map((t,z)=>e.jsxs("tr",{className:z%2===0?"bg-white":"bg-gray-50",children:[e.jsxs("td",{className:"px-4 py-3 whitespace-nowrap",children:[e.jsxs("a",{href:t.wordpressUrl,target:"_blank",rel:"noopener noreferrer",className:"text-blue-600 hover:text-blue-800 font-medium flex items-center",title:hs(t.pluginName),children:[pt(t.pluginName),e.jsx(Oe,{className:"h-3 w-3 ml-1"})]}),e.jsx("div",{className:"text-xs text-gray-500 font-mono",children:t.pluginSlug})]}),e.jsx("td",{className:"px-4 py-3 whitespace-nowrap text-sm text-gray-600",children:t.activeInstalls>0?t.activeInstalls.toLocaleString():"N/A"}),e.jsx("td",{className:"px-4 py-3 whitespace-nowrap text-sm text-gray-600",children:e.jsxs("div",{className:"flex items-center",children:[e.jsx(Me,{className:"h-4 w-4 text-yellow-400 mr-1"}),e.jsxs("span",{children:[t.rating>0?t.rating.toFixed(1):"N/A",t.numRatings>0&&e.jsxs("span",{className:"text-xs text-gray-400 ml-1",children:["(",t.numRatings,")"]})]})]})}),e.jsx("td",{className:"px-4 py-3 whitespace-nowrap text-sm text-gray-600",children:t.testedUpTo!=="N/A"?`WP ${t.testedUpTo}`:"N/A"}),e.jsx("td",{className:"px-4 py-3 whitespace-nowrap text-sm text-gray-600",children:t.lastUpdated!=="N/A"?(()=>{const J=new Date(t.lastUpdated);if(isNaN(J.getTime()))return"N/A";const Q=J.getDate().toString().padStart(2,"0"),K=(J.getMonth()+1).toString().padStart(2,"0"),ae=J.getFullYear();return`${K}-${Q}-${ae}`})():"N/A"})]},t.pluginSlug))})]})}):e.jsxs("div",{className:"p-8 text-center",children:[e.jsx(je,{className:"h-12 w-12 text-gray-300 mx-auto mb-4"}),e.jsx("h4",{className:"text-lg font-medium text-gray-900 mb-2",children:"No Related Plugins Found"}),e.jsx("p",{className:"text-gray-600",children:"No plugins found containing this keyword."})]})}),e.jsx("div",{className:"flex justify-end pt-4 border-t border-gray-200 flex-shrink-0",children:e.jsx("button",{onClick:()=>{me(!1),ce(null),Z([])},className:"px-4 py-2 bg-gray-600 text-white rounded-lg hover:bg-gray-700 transition-colors",children:"Close"})})]})})]})},yt=()=>{const[s,n]=r.useState("downloaded-data"),[a,p]=r.useState([]),[l,c]=r.useState(!1),[d,y]=r.useState(""),[$,b]=r.useState([]),[f,L]=r.useState(!1),[k,j]=r.useState("15"),[w,i]=r.useState({start:"",end:""}),[x,D]=r.useState(""),[P,M]=r.useState([]),[U,R]=r.useState(!1),[S,h]=r.useState({startDate:"",endDate:"",rating:[],page:1}),[g,v]=r.useState({totalReviews:0,averageRating:0,ratingDistribution:{}});r.useEffect(()=>{T()},[]);const T=async()=>{try{c(!0);const E=localStorage.getItem("adminToken"),O=await fetch("https://pluginsight.vercel.app/api/analytics/added-plugins",{headers:{Authorization:`Bearer ${E}`,"Content-Type":"application/json"}});if(!O.ok)throw new Error(`HTTP error! status: ${O.status}`);const V=await O.json();V.success?p(V.plugins):console.warn("Failed to load added plugins:",V.message)}catch(E){console.error("Error loading added plugins:",E),E.name==="TypeError"&&E.message.includes("Failed to fetch")?window.toast("Unable to connect to server. Please check if the backend is running.","error"):window.toast("Failed to load plugins","error")}finally{c(!1)}},_=[{id:"downloaded-data",name:"Downloaded Data",icon:xe},{id:"plugin-reviews",name:`Plugin Reviews${g.totalReviews>0?` (${g.totalReviews})`:""}`,icon:Ve}];return e.jsxs("div",{className:"space-y-6",children:[e.jsx("div",{className:"p-4",children:e.jsx("div",{className:"flex items-center",children:e.jsxs("h1",{className:"text-2xl font-bold text-gray-900 flex items-center",children:[e.jsx(ts,{className:"h-6 w-6 text-blue-600 mr-2"}),"Plugin Data Analysis"]})})}),e.jsxs("div",{className:"bg-white rounded-lg shadow-sm border border-gray-200",children:[e.jsx("div",{className:"border-b border-gray-200",children:e.jsx("nav",{className:"-mb-px flex space-x-8 px-6",children:_.map(E=>{const I=E.icon;return e.jsxs("button",{onClick:()=>n(E.id),className:`py-4 px-1 border-b-2 font-medium text-sm flex items-center space-x-2 ${s===E.id?"border-blue-500 text-blue-600":"border-transparent text-gray-500 hover:text-gray-700 hover:border-gray-300"}`,children:[e.jsx(I,{className:"h-4 w-4"}),e.jsx("span",{children:E.name})]},E.id)})})}),e.jsxs("div",{className:"p-6",children:[s==="downloaded-data"&&e.jsx(bt,{addedPlugins:a,selectedPlugin:d,setSelectedPlugin:y,downloadData:$,setDownloadData:b,downloadLoading:f,setDownloadLoading:L,dateRange:k,setDateRange:j,customDateRange:w,setCustomDateRange:i}),s==="plugin-reviews"&&e.jsx(jt,{addedPlugins:a,reviewsPlugin:x,setReviewsPlugin:D,reviews:P,setReviews:M,reviewsLoading:U,setReviewsLoading:R,reviewFilters:S,setReviewFilters:h,reviewStats:g,setReviewStats:v})]})]})]})},bt=({addedPlugins:s,selectedPlugin:n,setSelectedPlugin:a,downloadData:p,setDownloadData:l,downloadLoading:c,setDownloadLoading:d,dateRange:y,setDateRange:$,customDateRange:b,setCustomDateRange:f})=>{var j;const L=async()=>{try{d(!0),window.toast("Starting plugin download data fetch...","info");const w=localStorage.getItem("adminToken"),D=await(await fetch("https://pluginsight.vercel.app/api/analytics/download-data/refresh",{method:"POST",headers:{Authorization:`Bearer ${w}`,"Content-Type":"application/json"}})).json();D.success?(window.toast(D.message,"success"),n&&k(n)):window.toast(D.message||"Failed to refresh download data","error")}catch(w){console.error("Error refreshing download data:",w),window.toast("Failed to refresh download data","error")}finally{d(!1)}},k=async w=>{if(w)try{d(!0);const i=localStorage.getItem("adminToken"),x="https://pluginsight.vercel.app";let D=`${x}/api/analytics/download-data/${w}?days=${y}`;y==="custom"&&b.start&&b.end&&(D=`${x}/api/analytics/download-data/${w}?startDate=${b.start}&endDate=${b.end}`);const M=await(await fetch(D,{headers:{Authorization:`Bearer ${i}`,"Content-Type":"application/json"}})).json();if(M.success){const U=M.downloadData.map(R=>({date:new Date(R.date).toLocaleDateString("en-GB",{day:"2-digit",month:"2-digit"}),downloads:R.downloads,fullDate:R.date}));l(U)}}catch(i){console.error("Error loading download data:",i),window.toast("Failed to load download data","error")}finally{d(!1)}};return qe.useEffect(()=>{n&&k(n)},[n,y,b]),e.jsxs("div",{className:"space-y-6",children:[e.jsxs("div",{className:"flex flex-wrap items-center justify-between gap-4",children:[e.jsxs("div",{className:"flex items-center space-x-4",children:[e.jsxs("div",{children:[e.jsx("label",{className:"block text-sm font-medium text-gray-700 mb-1",children:"Select Plugin"}),e.jsxs("select",{value:n,onChange:w=>a(w.target.value),className:"px-3 py-2 border border-gray-300 rounded-lg focus:ring-2 focus:ring-blue-500 focus:border-transparent min-w-[200px]",children:[e.jsx("option",{value:"",children:"Choose a plugin"}),s.map(w=>e.jsx("option",{value:w.pluginSlug,children:w.displayName||w.pluginName},w.pluginSlug))]})]}),e.jsxs("div",{children:[e.jsx("label",{className:"block text-sm font-medium text-gray-700 mb-1",children:"Date Range"}),e.jsxs("select",{value:y,onChange:w=>$(w.target.value),className:"px-3 py-2 border border-gray-300 rounded-lg focus:ring-2 focus:ring-blue-500 focus:border-transparent",children:[e.jsx("option",{value:"",children:"Select date"}),e.jsx("option",{value:"7",children:"Last 7 days"}),e.jsx("option",{value:"15",children:"Last 15 days"}),e.jsx("option",{value:"30",children:"Last 30 days"}),e.jsx("option",{value:"custom",children:"Custom Range"})]})]}),y==="custom"&&e.jsxs("div",{className:"flex items-center space-x-2",children:[e.jsxs("div",{children:[e.jsx("label",{className:"block text-sm font-medium text-gray-700 mb-1",children:"Start Date"}),e.jsx("input",{type:"date",value:b.start,onChange:w=>f(i=>({...i,start:w.target.value})),className:"px-3 py-2 border border-gray-300 rounded-lg focus:ring-2 focus:ring-blue-500 focus:border-transparent"})]}),e.jsxs("div",{children:[e.jsx("label",{className:"block text-sm font-medium text-gray-700 mb-1",children:"End Date"}),e.jsx("input",{type:"date",value:b.end,onChange:w=>f(i=>({...i,end:w.target.value})),className:"px-3 py-2 border border-gray-300 rounded-lg focus:ring-2 focus:ring-blue-500 focus:border-transparent"})]})]})]}),e.jsxs("button",{onClick:L,disabled:c,className:"flex items-center px-4 py-2 bg-blue-600 text-white rounded-lg hover:bg-blue-700 transition-colors disabled:opacity-50",children:[e.jsx(oe,{className:`h-4 w-4 mr-2 ${c?"animate-spin":""}`}),"Refresh"]})]}),n&&p.length>0?e.jsxs("div",{className:"bg-gray-50 rounded-lg p-6",children:[e.jsxs("h3",{className:"text-lg font-semibold text-gray-900 mb-4",children:["Download Trends -"," ",((j=s.find(w=>w.pluginSlug===n))==null?void 0:j.displayName)||n]}),e.jsx("div",{className:"h-96",children:e.jsx(Ae,{width:"100%",height:"100%",children:e.jsxs(os,{data:p,children:[e.jsx(Te,{strokeDasharray:"3 3",stroke:"#f0f0f0"}),e.jsx(Ee,{dataKey:"date",stroke:"#6b7280",fontSize:12}),e.jsx(Le,{stroke:"#6b7280",fontSize:12,tickFormatter:w=>w.toLocaleString()}),e.jsx(Pe,{contentStyle:{backgroundColor:"#fff",border:"1px solid #e5e7eb",borderRadius:"8px"},formatter:w=>[w.toLocaleString(),"Downloads"],labelFormatter:(w,i)=>i&&i[0]?new Date(i[0].payload.fullDate).toLocaleDateString("en-GB",{weekday:"long",year:"numeric",month:"long",day:"numeric"}):w}),e.jsx(is,{dataKey:"downloads",fill:"#3b82f6",radius:[4,4,0,0],children:e.jsx(_e,{dataKey:"downloads",position:"top",fontSize:10,fill:"#3b82f6",formatter:w=>w.toLocaleString()})})]})})})]}):n&&c?e.jsx("div",{className:"flex items-center justify-center h-64",children:e.jsxs("div",{className:"text-center",children:[e.jsx(oe,{className:"h-8 w-8 text-blue-600 animate-spin mx-auto mb-2"}),e.jsx("p",{className:"text-gray-600",children:"Loading download data..."})]})}):n?e.jsx("div",{className:"flex items-center justify-center h-64",children:e.jsxs("div",{className:"text-center",children:[e.jsx(xe,{className:"h-16 w-16 text-gray-300 mx-auto mb-4"}),e.jsx("h4",{className:"text-lg font-medium text-gray-900 mb-2",children:"No Download Data"}),e.jsx("p",{className:"text-gray-600 mb-4",children:"No download data found for this plugin. Click refresh to fetch the latest data."})]})}):e.jsx("div",{className:"flex items-center justify-center h-64",children:e.jsxs("div",{className:"text-center",children:[e.jsx(xe,{className:"h-16 w-16 text-gray-300 mx-auto mb-4"}),e.jsx("h4",{className:"text-lg font-medium text-gray-900 mb-2",children:"Select a Plugin"}),e.jsx("p",{className:"text-gray-600",children:"Choose a plugin from the dropdown to view its download trends."})]})})]})},jt=({addedPlugins:s,reviewsPlugin:n,setReviewsPlugin:a,reviews:p,setReviews:l,reviewsLoading:c,setReviewsLoading:d,reviewFilters:y,setReviewFilters:$,reviewStats:b,setReviewStats:f})=>{const L=async()=>{try{d(!0),window.toast("Starting plugin reviews fetch...","info");const i=localStorage.getItem("adminToken"),D=await fetch("https://pluginsight.vercel.app/api/analytics/reviews/refresh",{method:"POST",headers:{Authorization:`Bearer ${i}`,"Content-Type":"application/json"}});if(!D.ok)throw new Error(`HTTP error! status: ${D.status}`);const P=await D.json();P.success?(window.toast(P.message,"success"),n&&k(n)):window.toast(P.message||"Failed to refresh reviews","error")}catch(i){console.error("Error refreshing reviews:",i),i.name==="TypeError"&&i.message.includes("Failed to fetch")?window.toast("Unable to connect to server. Please check if the backend is running.","error"):window.toast("Failed to refresh reviews","error")}finally{d(!1)}},k=async(i,x=1)=>{var D;if(i)try{d(!0);const P=new URLSearchParams({page:x.toString(),limit:"20"});y.startDate&&P.append("startDate",y.startDate),y.endDate&&P.append("endDate",y.endDate),y.rating.length>0&&y.rating.forEach(R=>P.append("rating",R.toString())),console.log(`Loading reviews for plugin: ${i} with params:`,P.toString());const M=await Ie(`/api/analytics/reviews/${i}?${P}`);if(!M.ok)throw new Error(`HTTP error! status: ${M.status}`);const U=await M.json();console.log("Reviews API response:",U),U.success?(console.log(`Loaded ${((D=U.reviews)==null?void 0:D.length)||0} reviews for ${i} (page ${x})`),l(x===1?U.reviews||[]:R=>[...R,...U.reviews||[]]),f(U.stats||{})):(console.warn("Reviews API returned success: false",U),x===1&&l([]),f({}))}catch(P){console.error("Error loading reviews:",P),P.name==="TypeError"&&P.message.includes("Failed to fetch")?window.toast("Unable to connect to server. Please check if the backend is running.","error"):window.toast(`Failed to load reviews: ${P.message}`,"error"),x===1&&(l([]),f({}))}finally{d(!1)}};qe.useEffect(()=>{n&&k(n)},[n,y]);const j=i=>Array.from({length:5},(x,D)=>e.jsx("span",{className:`text-lg ${D<i?"text-yellow-400":"text-gray-300"}`,children:"★"},D)),w=i=>{if(!i)return"";let x=i.replace(/<!\[CDATA\[/g,"").replace(/\]\]>/g,"").trim();x=x.replace(/^.*?Replies:\s*\d+\s*Rating:\s*\d+\s*stars?\s*/gi,"").replace(/^.*?Replies:\s*\d+\s*/gi,"").replace(/^.*?Rating:\s*\d+\s*stars?\s*/gi,"").replace(/Replies:\s*\d+\s*Rating:\s*\d+\s*stars?\s*/gi,"").replace(/Replies:\s*\d+\s*/gi,"").replace(/Rating:\s*\d+\s*stars?\s*/gi,"").trim();const D=document.createElement("textarea");return D.innerHTML=x,x=D.value,x=x.replace(/<[^>]*>/g,""),x=x.replace(/\s+/g," ").trim(),x};return e.jsxs("div",{className:"space-y-6",children:[e.jsxs("div",{className:"flex flex-wrap items-center justify-between gap-4",children:[e.jsxs("div",{className:"flex items-center space-x-4",children:[e.jsxs("div",{children:[e.jsx("label",{className:"block text-sm font-medium text-gray-700 mb-1",children:"Select Plugin"}),e.jsxs("select",{value:n,onChange:i=>a(i.target.value),className:"px-3 py-2 border border-gray-300 rounded-lg focus:ring-2 focus:ring-blue-500 focus:border-transparent min-w-[200px]",children:[e.jsx("option",{value:"",children:"Choose a plugin"}),s.map(i=>e.jsx("option",{value:i.pluginSlug,children:i.displayName||i.pluginName},i.pluginSlug))]})]}),e.jsxs("div",{className:"flex items-center space-x-2",children:[e.jsxs("div",{children:[e.jsx("label",{className:"block text-sm font-medium text-gray-700 mb-1",children:"Start Date"}),e.jsx("input",{type:"date",value:y.startDate,onChange:i=>$(x=>({...x,startDate:i.target.value})),className:"px-3 py-2 border border-gray-300 rounded-lg focus:ring-2 focus:ring-blue-500 focus:border-transparent"})]}),e.jsxs("div",{children:[e.jsx("label",{className:"block text-sm font-medium text-gray-700 mb-1",children:"End Date"}),e.jsx("input",{type:"date",value:y.endDate,onChange:i=>$(x=>({...x,endDate:i.target.value})),className:"px-3 py-2 border border-gray-300 rounded-lg focus:ring-2 focus:ring-blue-500 focus:border-transparent"})]})]}),e.jsxs("div",{children:[e.jsx("label",{className:"block text-sm font-medium text-gray-700 mb-1",children:"Rating Filter"}),e.jsxs("select",{value:y.rating.length===1?y.rating[0]:"",onChange:i=>{const x=i.target.value;$(x===""?D=>({...D,rating:[]}):D=>({...D,rating:[parseInt(x)]}))},className:"px-3 py-2 border border-gray-300 rounded-lg focus:ring-2 focus:ring-blue-500 focus:border-transparent",children:[e.jsx("option",{value:"",children:"All ratings"}),e.jsx("option",{value:"5",children:"5 stars"}),e.jsx("option",{value:"4",children:"4 stars"}),e.jsx("option",{value:"3",children:"3 stars"}),e.jsx("option",{value:"2",children:"2 stars"}),e.jsx("option",{value:"1",children:"1 star"})]})]})]}),e.jsxs("button",{onClick:L,disabled:c,className:"flex items-center px-4 py-2 bg-blue-600 text-white rounded-lg hover:bg-blue-700 transition-colors disabled:opacity-50",children:[e.jsx(oe,{className:`h-4 w-4 mr-2 ${c?"animate-spin":""}`}),"Refresh"]})]}),n&&p.length>0?e.jsxs("div",{className:"space-y-6",children:[e.jsx("div",{className:"bg-gray-50 rounded-lg p-4",children:e.jsx("div",{className:"grid grid-cols-1 md:grid-cols-3 gap-4",children:e.jsx("div",{className:"text-center",children:e.jsxs("div",{className:"text-sm text-gray-600 flex items-center gap-2",children:["Total",e.jsx("span",{className:"text-2xl font-bold text-gray-900",children:b.totalReviews}),"Reviews"]})})})}),e.jsx("div",{className:"h-[460px] overflow-y-auto space-y-4",children:p.map((i,x)=>e.jsxs("div",{className:"bg-white border border-gray-200 rounded-lg p-6 animate-fade-in-up",style:{animationDelay:`${x*100}ms`,animationFillMode:"both"},children:[e.jsxs("div",{className:"flex items-start justify-between mb-3",children:[e.jsxs("div",{className:"flex items-center space-x-3",children:[e.jsx("div",{className:"flex",children:j(i.rating)}),e.jsxs("div",{className:"text-sm text-gray-500",children:["by ",i.author," •"," ",new Date(i.date).toLocaleDateString("en-GB")]})]}),i.reviewUrl&&e.jsx("a",{href:i.reviewUrl,target:"_blank",rel:"noopener noreferrer",className:"inline-flex items-center text-sm font-medium text-blue-500 rounded-lg hover:text-blue-700 transition-all duration-200",children:e.jsx(Oe,{className:"h-4 w-4"})})]}),e.jsx("h4",{className:"font-semibold text-gray-900 mb-2",children:i.title}),e.jsx("div",{className:"text-gray-700 leading-relaxed",children:w(i.content)})]},i._id||x))})]}):n&&c?e.jsx("div",{className:"flex items-center justify-center h-64",children:e.jsxs("div",{className:"text-center",children:[e.jsx(oe,{className:"h-8 w-8 text-blue-600 animate-spin mx-auto mb-2"}),e.jsx("p",{className:"text-gray-600",children:"Loading reviews..."})]})}):n?e.jsx("div",{className:"flex items-center justify-center h-64",children:e.jsxs("div",{className:"text-center",children:[e.jsx(Ve,{className:"h-16 w-16 text-gray-300 mx-auto mb-4"}),e.jsx("h4",{className:"text-lg font-medium text-gray-900 mb-2",children:"No Reviews Found"}),e.jsx("p",{className:"text-gray-600 mb-4",children:"No reviews found for this plugin. Click refresh to fetch the latest reviews."})]})}):e.jsx("div",{className:"flex items-center justify-center h-64",children:e.jsxs("div",{className:"text-center",children:[e.jsx(Ve,{className:"h-16 w-16 text-gray-300 mx-auto mb-4"}),e.jsx("h4",{className:"text-lg font-medium text-gray-900 mb-2",children:"Select a Plugin"}),e.jsx("p",{className:"text-gray-600",children:"Choose a plugin from the dropdown to view its reviews."})]})})]})},wt=()=>{const{user:s}=we(),[n,a]=r.useState([]),[p,l]=r.useState(!0),[c,d]=r.useState(""),[y,$]=r.useState(""),[b,f]=r.useState(""),[L,k]=r.useState(!1),[j,w]=r.useState(!1),[i,x]=r.useState(!1),[D,P]=r.useState(!1),[M,U]=r.useState(!1),[R,S]=r.useState(null),[h,g]=r.useState({current:1,pages:1,total:0,limit:10}),[v,T]=r.useState({name:"",email:"",password:"",role:"member"}),[_,E]=r.useState({newPassword:"",confirmPassword:""}),[I,O]=r.useState({canAddPlugins:!1,canDeletePlugins:!1,canAddKeywords:!1,canAddUsers:!1}),V=async(m=1,B="",Z="")=>{try{l(!0);const u=localStorage.getItem("adminToken");if(!u){d("Authentication token not found. Please login again.");return}const F=new URLSearchParams({page:m.toString(),limit:"10",...B&&{search:B},...Z&&{role:Z}}),H=await fetch(`https://pluginsight.vercel.app/api/users?${F}`,{headers:{Authorization:`Bearer ${u}`,"Content-Type":"application/json"}});if(!H.ok){if(H.status===401){d("Authentication failed. Please login again."),localStorage.removeItem("adminToken");return}throw new Error(`HTTP error! status: ${H.status}`)}const ee=await H.json();ee.success?(a(ee.users),g(ee.pagination),d("")):d(ee.message||"Failed to fetch users")}catch(u){console.error("Fetch users error:",u),u.name==="TypeError"&&u.message.includes("Failed to fetch")?d("Unable to connect to server. Please check if the backend is running."):d("Failed to fetch users. Please try again.")}finally{l(!1)}};r.useEffect(()=>{V()},[]);const G=()=>{V(1,y,b)},X=async m=>{m.preventDefault();try{const B=localStorage.getItem("adminToken"),u=await fetch("https://pluginsight.vercel.app/api/users",{method:"POST",headers:{Authorization:`Bearer ${B}`,"Content-Type":"application/json"},body:JSON.stringify(v)});if(!u.ok)throw new Error(`HTTP error! status: ${u.status}`);const F=await u.json();F.success?(k(!1),T({name:"",email:"",password:"",role:"member"}),V(h.current,y,b)):d(F.message||"Failed to create user")}catch(B){console.error("Add user error:",B),B.name==="TypeError"&&B.message.includes("Failed to fetch")?d("Unable to connect to server. Please check if the backend is running."):d("Failed to create user")}},te=async m=>{m.preventDefault();try{const B=localStorage.getItem("adminToken"),u=await fetch(`https://pluginsight.vercel.app/api/users/${R._id}`,{method:"PUT",headers:{Authorization:`Bearer ${B}`,"Content-Type":"application/json"},body:JSON.stringify({name:v.name,email:v.email,role:v.role,isActive:v.isActive})});if(!u.ok)throw new Error(`HTTP error! status: ${u.status}`);const F=await u.json();F.success?(w(!1),S(null),V(h.current,y,b)):d(F.message||"Failed to update user")}catch(B){console.error("Edit user error:",B),B.name==="TypeError"&&B.message.includes("Failed to fetch")?d("Unable to connect to server. Please check if the backend is running."):d("Failed to update user")}},ie=m=>{if(s&&s._id===m._id){window.toast("You cannot delete your own account","error");return}S(m),P(!0)},he=async()=>{if(R)try{const m=localStorage.getItem("adminToken"),Z=await fetch(`https://pluginsight.vercel.app/api/users/${R._id}`,{method:"DELETE",headers:{Authorization:`Bearer ${m}`,"Content-Type":"application/json"}});if(!Z.ok)throw new Error(`HTTP error! status: ${Z.status}`);const u=await Z.json();u.success?(V(h.current,y,b),P(!1),S(null)):d(u.message||"Failed to delete user")}catch(m){console.error("Delete user error:",m),m.name==="TypeError"&&m.message.includes("Failed to fetch")?d("Unable to connect to server. Please check if the backend is running."):d("Failed to delete user")}},ne=async m=>{if(m.preventDefault(),_.newPassword!==_.confirmPassword){d("Passwords do not match");return}try{const B=localStorage.getItem("adminToken"),u=await fetch(`https://pluginsight.vercel.app/api/users/${R._id}/reset-password`,{method:"PUT",headers:{Authorization:`Bearer ${B}`,"Content-Type":"application/json"},body:JSON.stringify({newPassword:_.newPassword})});if(!u.ok)throw new Error(`HTTP error! status: ${u.status}`);const F=await u.json();F.success?(x(!1),S(null),E({newPassword:"",confirmPassword:""}),alert("Password reset successfully")):d(F.message||"Failed to reset password")}catch(B){console.error("Reset password error:",B),B.name==="TypeError"&&B.message.includes("Failed to fetch")?d("Unable to connect to server. Please check if the backend is running."):d("Failed to reset password")}},Ne=m=>{S(m),T({name:m.name,email:m.email,role:m.role,isActive:m.isActive}),w(!0)},ge=m=>{S(m),E({newPassword:"",confirmPassword:""}),x(!0)},pe=m=>{var B,Z,u,F;S(m),O({canAddPlugins:((B=m.permissions)==null?void 0:B.canAddPlugins)||!1,canDeletePlugins:((Z=m.permissions)==null?void 0:Z.canDeletePlugins)||!1,canAddKeywords:((u=m.permissions)==null?void 0:u.canAddKeywords)||!1,canAddUsers:((F=m.permissions)==null?void 0:F.canAddUsers)||!1}),U(!0)},me=m=>{switch(m){case"superadmin":return"bg-red-100 text-red-800";case"admin":return"bg-blue-100 text-blue-800";case"member":return"bg-green-100 text-green-800";default:return"bg-gray-100 text-gray-800"}},le=m=>s.role==="superadmin"||s.role==="admin"&&m.role==="member"?s._id!==m._id:!1,ce=m=>!(s.role==="admin"&&m.role==="superadmin"),be=m=>s._id===m._id?!1:s.role==="superadmin"||s.role==="admin"&&m.role==="member";return e.jsxs("div",{className:"space-y-6",children:[e.jsx("div",{className:"flex justify-between items-center p-4",children:e.jsxs("div",{children:[e.jsxs("h1",{className:"text-2xl font-bold text-gray-900 flex items-center",children:[e.jsx(Bs,{className:"h-6 w-6 text-blue-600 mr-2"}),"Team Members"]}),e.jsx("p",{className:"text-sm text-gray-600",children:"Manage system users and their permissions"})]})}),e.jsxs("div",{className:"bg-white rounded-lg p-6 shadow-sm border border-gray-200 flex justify-between items-center",children:[e.jsx("div",{children:["admin","superadmin"].includes(s==null?void 0:s.role)&&e.jsxs("button",{onClick:()=>k(!0),className:"bg-blue-600 text-white px-4 py-2 rounded-lg hover:bg-blue-700 flex items-center gap-2",children:[e.jsx(Se,{className:"h-4 w-4"}),"Add User"]})}),e.jsxs("div",{className:"flex gap-4 items-center",children:[e.jsxs("div",{className:"flex relative",children:[e.jsx(je,{className:"absolute left-3 top-1/2 transform -translate-y-1/2 h-4 w-4 text-gray-400"}),e.jsx("input",{type:"text",placeholder:"Search users...",value:y,onChange:m=>$(m.target.value),className:"w-full pl-10 pr-4 py-2 border border-gray-300 rounded-lg focus:outline-none focus:ring-2 focus:ring-blue-500"})]}),e.jsxs("div",{className:"relative",children:[e.jsx(Is,{className:"absolute left-3 top-1/2 transform -translate-y-1/2 h-4 w-4 text-gray-400"}),e.jsxs("select",{value:b,onChange:m=>f(m.target.value),className:"pl-10 pr-8 py-2 border border-gray-300 rounded-lg focus:outline-none focus:ring-2 focus:ring-blue-500",children:[e.jsx("option",{value:"",children:"All Roles"}),e.jsx("option",{value:"superadmin",children:"Super Admin"}),e.jsx("option",{value:"admin",children:"Admin"}),e.jsx("option",{value:"member",children:"Member"})]})]}),e.jsx("button",{onClick:G,className:"bg-blue-600 text-white px-4 py-2 rounded-lg hover:bg-blue-700",children:"Search"})]})]}),c&&e.jsx("div",{className:"bg-red-50 border border-red-200 text-red-600 px-4 py-3 rounded-lg",children:c}),e.jsx("div",{className:"bg-white rounded-lg shadow-sm border border-gray-200 overflow-hidden",children:p?e.jsxs("div",{className:"p-8 text-center",children:[e.jsx("div",{className:"animate-spin rounded-full h-8 w-8 border-b-2 border-blue-600 mx-auto"}),e.jsx("p",{className:"mt-2 text-gray-600",children:"Loading users..."})]}):n.length===0?e.jsxs("div",{className:"p-8 text-center",children:[e.jsx(Re,{className:"h-16 w-16 text-gray-400 mx-auto mb-4"}),e.jsx("h3",{className:"text-lg font-medium text-gray-900 mb-2",children:"No users found"}),e.jsx("p",{className:"text-gray-600",children:"Try adjusting your search criteria."})]}):e.jsx("div",{className:"overflow-x-auto",children:e.jsxs("table",{className:"w-full",children:[e.jsx("thead",{className:"bg-gray-50",children:e.jsxs("tr",{children:[e.jsx("th",{className:"px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider",children:"User"}),e.jsx("th",{className:"px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider",children:"Role"}),e.jsx("th",{className:"px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider",children:"Status"}),e.jsx("th",{className:"px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider",children:"Created"}),e.jsx("th",{className:"px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider",children:"Actions"})]})}),e.jsx("tbody",{className:"bg-white divide-y divide-gray-200",children:n.filter(ce).map(m=>e.jsxs("tr",{className:"hover:bg-gray-50",children:[e.jsx("td",{className:"px-6 py-4 whitespace-nowrap",children:e.jsxs("div",{children:[e.jsx("div",{className:"text-sm font-medium text-gray-900",children:m.name}),e.jsx("div",{className:"text-sm text-gray-500",children:m.email})]})}),e.jsx("td",{className:"px-6 py-4 whitespace-nowrap",children:e.jsx("span",{className:`inline-flex px-2 py-1 text-xs font-semibold rounded-full ${me(m.role)}`,children:m.role})}),e.jsx("td",{className:"px-6 py-4 whitespace-nowrap",children:e.jsx("span",{className:`inline-flex px-2 py-1 text-xs font-semibold rounded-full ${m.isActive?"bg-green-100 text-green-800":"bg-red-100 text-red-800"}`,children:m.isActive?"Active":"Inactive"})}),e.jsx("td",{className:"px-6 py-4 whitespace-nowrap text-sm text-gray-500",children:new Date(m.createdAt).toLocaleDateString()}),e.jsx("td",{className:"px-6 py-4 whitespace-nowrap text-sm font-medium",children:e.jsx("div",{className:"flex items-center gap-2",children:le(m)&&e.jsxs(e.Fragment,{children:[e.jsx("button",{onClick:()=>Ne(m),className:"text-blue-600 hover:text-blue-900",title:"Edit User",children:e.jsx(ns,{className:"h-4 w-4"})}),e.jsx("button",{onClick:()=>ge(m),className:"text-yellow-600 hover:text-yellow-900",title:"Reset Password",children:e.jsx(Ms,{className:"h-4 w-4"})}),e.jsx("button",{onClick:()=>pe(m),className:"text-purple-600 hover:text-purple-900",title:"Manage Permissions",children:e.jsx(We,{className:"h-4 w-4"})}),be(m)&&e.jsx("button",{onClick:()=>ie(m),className:"text-red-600 hover:text-red-900",title:"Delete User",children:e.jsx($e,{className:"h-4 w-4"})})]})})})]},m._id))})]})})}),h.pages>1&&e.jsxs("div",{className:"flex justify-center items-center gap-2",children:[e.jsx("button",{onClick:()=>V(h.current-1,y,b),disabled:h.current===1,className:"px-3 py-1 border border-gray-300 rounded disabled:opacity-50",children:"Previous"}),e.jsxs("span",{className:"text-sm text-gray-600",children:["Page ",h.current," of ",h.pages]}),e.jsx("button",{onClick:()=>V(h.current+1,y,b),disabled:h.current===h.pages,className:"px-3 py-1 border border-gray-300 rounded disabled:opacity-50",children:"Next"})]}),L&&e.jsx("div",{className:"fixed inset-0 bg-black bg-opacity-50 flex items-center justify-center z-50",children:e.jsxs("div",{className:"bg-white rounded-lg p-6 w-full max-w-md",children:[e.jsx("h2",{className:"text-xl font-bold mb-4",children:"Add New User"}),e.jsxs("form",{onSubmit:X,className:"space-y-4",children:[e.jsxs("div",{children:[e.jsx("label",{className:"block text-sm font-medium text-gray-700 mb-1",children:"Name"}),e.jsx("input",{type:"text",value:v.name,onChange:m=>T({...v,name:m.target.value}),className:"w-full px-3 py-2 border border-gray-300 rounded-lg focus:outline-none focus:ring-2 focus:ring-blue-500",required:!0})]}),e.jsxs("div",{children:[e.jsx("label",{className:"block text-sm font-medium text-gray-700 mb-1",children:"Email"}),e.jsx("input",{type:"email",value:v.email,onChange:m=>T({...v,email:m.target.value}),className:"w-full px-3 py-2 border border-gray-300 rounded-lg focus:outline-none focus:ring-2 focus:ring-blue-500",required:!0})]}),e.jsxs("div",{children:[e.jsx("label",{className:"block text-sm font-medium text-gray-700 mb-1",children:"Password"}),e.jsx("input",{type:"password",value:v.password,onChange:m=>T({...v,password:m.target.value}),className:"w-full px-3 py-2 border border-gray-300 rounded-lg focus:outline-none focus:ring-2 focus:ring-blue-500",required:!0})]}),e.jsxs("div",{children:[e.jsx("label",{className:"block text-sm font-medium text-gray-700 mb-1",children:"Role"}),e.jsxs("select",{value:v.role,onChange:m=>T({...v,role:m.target.value}),className:"w-full px-3 py-2 border border-gray-300 rounded-lg focus:outline-none focus:ring-2 focus:ring-blue-500",children:[e.jsx("option",{value:"member",children:"Member"}),(s==null?void 0:s.role)==="superadmin"&&e.jsxs(e.Fragment,{children:[e.jsx("option",{value:"admin",children:"Admin"}),e.jsx("option",{value:"superadmin",children:"Super Admin"})]})]})]}),e.jsxs("div",{className:"flex justify-end gap-2 pt-4",children:[e.jsx("button",{type:"button",onClick:()=>{k(!1),T({name:"",email:"",password:"",role:"member"})},className:"px-4 py-2 text-gray-600 border border-gray-300 rounded-lg hover:bg-gray-50",children:"Cancel"}),e.jsx("button",{type:"submit",className:"px-4 py-2 bg-blue-600 text-white rounded-lg hover:bg-blue-700",children:"Add User"})]})]})]})}),j&&R&&e.jsx("div",{className:"fixed inset-0 bg-black bg-opacity-50 flex items-center justify-center z-50",children:e.jsxs("div",{className:"bg-white rounded-lg p-6 w-full max-w-md",children:[e.jsx("h2",{className:"text-xl font-bold mb-4",children:"Edit User"}),e.jsxs("form",{onSubmit:te,className:"space-y-4",children:[e.jsxs("div",{children:[e.jsx("label",{className:"block text-sm font-medium text-gray-700 mb-1",children:"Name"}),e.jsx("input",{type:"text",value:v.name,onChange:m=>T({...v,name:m.target.value}),className:"w-full px-3 py-2 border border-gray-300 rounded-lg focus:outline-none focus:ring-2 focus:ring-blue-500",required:!0})]}),e.jsxs("div",{children:[e.jsx("label",{className:"block text-sm font-medium text-gray-700 mb-1",children:"Email"}),e.jsx("input",{type:"email",value:v.email,onChange:m=>T({...v,email:m.target.value}),className:"w-full px-3 py-2 border border-gray-300 rounded-lg focus:outline-none focus:ring-2 focus:ring-blue-500",required:!0})]}),e.jsxs("div",{children:[e.jsx("label",{className:"block text-sm font-medium text-gray-700 mb-1",children:"Role"}),e.jsxs("select",{value:v.role,onChange:m=>T({...v,role:m.target.value}),className:"w-full px-3 py-2 border border-gray-300 rounded-lg focus:outline-none focus:ring-2 focus:ring-blue-500",disabled:(s==null?void 0:s.role)==="admin"&&["admin","superadmin"].includes(R.role),children:[e.jsx("option",{value:"member",children:"Member"}),(s==null?void 0:s.role)==="superadmin"&&e.jsxs(e.Fragment,{children:[e.jsx("option",{value:"admin",children:"Admin"}),e.jsx("option",{value:"superadmin",children:"Super Admin"})]})]})]}),e.jsx("div",{children:e.jsxs("label",{className:"flex items-center",children:[e.jsx("input",{type:"checkbox",checked:v.isActive,onChange:m=>T({...v,isActive:m.target.checked}),className:"mr-2"}),e.jsx("span",{className:"text-sm font-medium text-gray-700",children:"Active"})]})}),e.jsxs("div",{className:"flex justify-end gap-2 pt-4",children:[e.jsx("button",{type:"button",onClick:()=>{w(!1),S(null)},className:"px-4 py-2 text-gray-600 border border-gray-300 rounded-lg hover:bg-gray-50",children:"Cancel"}),e.jsx("button",{type:"submit",className:"px-4 py-2 bg-blue-600 text-white rounded-lg hover:bg-blue-700",children:"Update User"})]})]})]})}),i&&R&&e.jsx("div",{className:"fixed inset-0 bg-black bg-opacity-50 flex items-center justify-center z-50",children:e.jsxs("div",{className:"bg-white rounded-lg p-6 w-full max-w-md",children:[e.jsx("h2",{className:"text-xl font-bold mb-4",children:"Reset Password"}),e.jsxs("p",{className:"text-gray-600 mb-4",children:["Reset password for: ",e.jsx("strong",{children:R.name})]}),e.jsxs("form",{onSubmit:ne,className:"space-y-4",children:[e.jsxs("div",{children:[e.jsx("label",{className:"block text-sm font-medium text-gray-700 mb-1",children:"New Password"}),e.jsx("input",{type:"password",value:_.newPassword,onChange:m=>E({..._,newPassword:m.target.value}),className:"w-full px-3 py-2 border border-gray-300 rounded-lg focus:outline-none focus:ring-2 focus:ring-blue-500",required:!0,minLength:"6"})]}),e.jsxs("div",{children:[e.jsx("label",{className:"block text-sm font-medium text-gray-700 mb-1",children:"Confirm Password"}),e.jsx("input",{type:"password",value:_.confirmPassword,onChange:m=>E({..._,confirmPassword:m.target.value}),className:"w-full px-3 py-2 border border-gray-300 rounded-lg focus:outline-none focus:ring-2 focus:ring-blue-500",required:!0,minLength:"6"})]}),e.jsxs("div",{className:"flex justify-end gap-2 pt-4",children:[e.jsx("button",{type:"button",onClick:()=>{x(!1),S(null),E({newPassword:"",confirmPassword:""})},className:"px-4 py-2 text-gray-600 border border-gray-300 rounded-lg hover:bg-gray-50",children:"Cancel"}),e.jsx("button",{type:"submit",className:"px-4 py-2 bg-red-600 text-white rounded-lg hover:bg-red-700",children:"Reset Password"})]})]})]})}),D&&e.jsx("div",{className:"fixed inset-0 bg-black bg-opacity-50 flex items-center justify-center z-50",children:e.jsxs("div",{className:"bg-white rounded-lg p-6 max-w-md w-full mx-4",children:[e.jsxs("div",{className:"flex items-center mb-4",children:[e.jsx("div",{className:"flex-shrink-0 w-10 h-10 bg-red-100 rounded-full flex items-center justify-center",children:e.jsx(Os,{className:"h-6 w-6 text-red-600"})}),e.jsxs("div",{className:"ml-4",children:[e.jsx("h3",{className:"text-lg font-medium text-gray-900",children:"Delete User"}),e.jsx("p",{className:"text-sm text-gray-500",children:"Are you sure you want to delete this user? This action cannot be undone."})]})]}),R&&e.jsxs("div",{className:"bg-gray-50 rounded-lg p-3 mb-4",children:[e.jsx("p",{className:"text-sm font-medium text-gray-900",children:R.name}),e.jsx("p",{className:"text-sm text-gray-500",children:R.email}),e.jsx("span",{className:`inline-flex px-2 py-1 text-xs font-semibold rounded-full mt-1 ${me(R.role)}`,children:R.role})]}),e.jsxs("div",{className:"flex justify-end gap-3",children:[e.jsx("button",{onClick:()=>{P(!1),S(null)},className:"px-4 py-2 text-gray-600 border border-gray-300 rounded-lg hover:bg-gray-50 transition-colors",children:"Cancel"}),e.jsx("button",{onClick:he,className:"px-4 py-2 bg-red-600 text-white rounded-lg hover:bg-red-700 transition-colors",children:"Delete User"})]})]})}),e.jsx(ye,{isOpen:M,onClose:()=>{U(!1),S(null)},title:"Manage User Permissions",children:R&&e.jsxs("div",{className:"space-y-4",children:[e.jsxs("div",{className:"bg-gray-50 rounded-lg p-3 mb-4",children:[e.jsx("p",{className:"text-sm font-medium text-gray-900",children:R.name}),e.jsx("p",{className:"text-sm text-gray-500",children:R.email}),e.jsx("span",{className:`inline-flex px-2 py-1 text-xs font-semibold rounded-full mt-1 ${me(R.role)}`,children:R.role})]}),e.jsxs("div",{className:"space-y-3",children:[e.jsxs("div",{className:"flex items-center justify-between",children:[e.jsx("label",{className:"text-sm font-medium text-gray-700",children:"Add Plugins"}),e.jsx("input",{type:"checkbox",checked:I.canAddPlugins,onChange:m=>O(B=>({...B,canAddPlugins:m.target.checked})),className:"h-4 w-4 text-blue-600 focus:ring-blue-500 border-gray-300 rounded"})]}),e.jsxs("div",{className:"flex items-center justify-between",children:[e.jsx("label",{className:"text-sm font-medium text-gray-700",children:"Delete Plugins"}),e.jsx("input",{type:"checkbox",checked:I.canDeletePlugins,onChange:m=>O(B=>({...B,canDeletePlugins:m.target.checked})),className:"h-4 w-4 text-blue-600 focus:ring-blue-500 border-gray-300 rounded"})]}),e.jsxs("div",{className:"flex items-center justify-between",children:[e.jsx("label",{className:"text-sm font-medium text-gray-700",children:"Add Keywords"}),e.jsx("input",{type:"checkbox",checked:I.canAddKeywords,onChange:m=>O(B=>({...B,canAddKeywords:m.target.checked})),className:"h-4 w-4 text-blue-600 focus:ring-blue-500 border-gray-300 rounded"})]}),e.jsxs("div",{className:"flex items-center justify-between",children:[e.jsx("label",{className:"text-sm font-medium text-gray-700",children:"Add Users"}),e.jsx("input",{type:"checkbox",checked:I.canAddUsers,onChange:m=>O(B=>({...B,canAddUsers:m.target.checked})),className:"h-4 w-4 text-blue-600 focus:ring-blue-500 border-gray-300 rounded"})]})]}),e.jsxs("div",{className:"flex justify-end gap-2 pt-4",children:[e.jsx("button",{onClick:()=>{U(!1),S(null)},className:"px-4 py-2 text-gray-600 border border-gray-300 rounded-lg hover:bg-gray-50",children:"Cancel"}),e.jsx("button",{onClick:()=>{window.toast("Permissions updated successfully","success"),U(!1),S(null)},className:"px-4 py-2 bg-blue-600 text-white rounded-lg hover:bg-blue-700",children:"Save Permissions"})]})]})})]})},Nt=()=>{const{user:s}=we(),[n,a]=r.useState(!1),[p,l]=r.useState(""),[c,d]=r.useState(""),[y,$]=r.useState({member:{canAddPlugins:!1,canDeletePlugins:!1,canAddKeywords:!1,canAddUsers:!1},admin:{canAddPlugins:!0,canDeletePlugins:!0,canAddKeywords:!0,canAddUsers:!0},superadmin:{canAddPlugins:!0,canDeletePlugins:!0,canAddKeywords:!0,canAddUsers:!0}});r.useEffect(()=>{b()},[]);const b=async()=>{try{const i=localStorage.getItem("adminToken"),P=await(await fetch("https://pluginsight.vercel.app/api/settings/permissions",{headers:{Authorization:`Bearer ${i}`,"Content-Type":"application/json"}})).json();P.success&&$(P.permissions)}catch(i){console.error("Error loading permissions:",i)}},f=(i,x,D)=>{$(P=>({...P,[i]:{...P[i],[x]:D}}))},L=async()=>{a(!0),d(""),l("");try{const i=localStorage.getItem("adminToken"),P=await(await fetch("https://pluginsight.vercel.app/api/settings/permissions",{method:"PUT",headers:{Authorization:`Bearer ${i}`,"Content-Type":"application/json"},body:JSON.stringify({permissions:y})})).json();P.success?(l("Permissions updated successfully"),setTimeout(()=>l(""),3e3)):d(P.message||"Failed to update permissions")}catch(i){console.error("Error saving permissions:",i),d("Failed to update permissions")}finally{a(!1)}},k={canAddPlugins:"Add Plugins",canDeletePlugins:"Delete Plugins",canAddKeywords:"Add Keywords",canAddUsers:"Add Users"},j={member:"Member",admin:"Admin",superadmin:"Super Admin"};if(!s)return e.jsx("div",{className:"space-y-6",children:e.jsx("div",{className:"bg-white rounded-lg p-8 shadow-sm border border-gray-200",children:e.jsxs("div",{className:"text-center",children:[e.jsx(Be,{className:"h-16 w-16 text-red-400 mx-auto mb-4"}),e.jsx("h1",{className:"text-2xl font-bold text-gray-900 mb-2",children:"Access Denied"}),e.jsx("p",{className:"text-gray-600",children:"Please login to access settings."})]})})});const w=["admin","superadmin"].includes(s.role);return e.jsxs("div",{className:"space-y-6",children:[e.jsx("div",{className:"p-4",children:e.jsx("div",{className:"flex items-center justify-between",children:e.jsxs("div",{children:[e.jsxs("h1",{className:"text-2xl font-bold text-gray-900 flex items-center",children:[e.jsx(We,{className:"h-8 w-8 text-blue-600 mr-3"}),"Settings"]}),e.jsx("p",{className:"text-sm text-gray-600 mt-1",children:"Manage application settings and user permissions"})]})})}),p&&e.jsx("div",{className:"bg-green-50 border border-green-200 text-green-600 px-4 py-3 rounded-lg",children:p}),c&&e.jsx("div",{className:"bg-red-50 border border-red-200 text-red-600 px-4 py-3 rounded-lg",children:c}),e.jsxs("div",{className:"bg-white rounded-lg shadow-sm border border-gray-200 w-1/2",children:[e.jsxs("div",{className:"p-6 border-b border-gray-200 flex items-center justify-between",children:[e.jsxs("div",{children:[e.jsxs("h2",{className:"text-lg font-semibold text-gray-900 flex items-center",children:[e.jsx(Re,{className:"h-5 w-5 text-gray-600 mr-2"}),"User Permissions"]}),e.jsx("p",{className:"text-sm text-gray-600 mt-1",children:"Configure what actions each user role can perform"})]}),e.jsx("div",{className:"flex justify-end",children:w?e.jsxs("button",{onClick:L,disabled:n,className:"flex items-center px-4 py-2 bg-blue-600 text-white rounded-lg hover:bg-blue-700 disabled:opacity-50 transition-colors",children:[e.jsx(ls,{className:"h-4 w-4 mr-2"}),n?"Saving...":"Save"]}):e.jsx("div",{className:"text-sm text-gray-500 bg-gray-100 px-4 py-2 rounded-lg",children:"View Only - Admin privileges required to edit"})})]}),e.jsx("div",{className:"p-6",children:e.jsx("div",{className:"overflow-x-auto",children:e.jsxs("table",{className:"min-w-full",children:[e.jsx("thead",{children:e.jsxs("tr",{className:"border-b border-gray-200",children:[e.jsx("th",{className:"text-left py-3 px-4 font-medium text-gray-900",children:"Permission"}),Object.keys(j).map(i=>e.jsx("th",{className:"text-center py-3 px-4 font-medium text-gray-900",children:j[i]},i))]})}),e.jsx("tbody",{className:"divide-y divide-gray-200",children:Object.keys(k).map(i=>e.jsxs("tr",{className:"hover:bg-gray-50",children:[e.jsx("td",{className:"py-4 px-4 text-sm font-medium text-gray-900",children:k[i]}),Object.keys(j).map(x=>{var D;return e.jsx("td",{className:"py-4 px-4 text-center",children:e.jsx("input",{type:"checkbox",checked:((D=y[x])==null?void 0:D[i])||!1,onChange:P=>f(x,i,P.target.checked),disabled:!w||x==="superadmin",className:"h-4 w-4 text-blue-600 focus:ring-blue-500 border-gray-300 rounded disabled:opacity-50"})},x)})]},i))})]})})})]})]})},vt=()=>{const{user:s}=we(),[n,a]=r.useState(!1),[p,l]=r.useState(!1),[c,d]=r.useState({name:(s==null?void 0:s.name)||"",email:(s==null?void 0:s.email)||"",newPassword:"",confirmPassword:""}),y=async k=>{k.preventDefault(),l(!0);try{if(c.newPassword){if(c.newPassword!==c.confirmPassword){window.toast("New passwords do not match","error"),l(!1);return}if(c.newPassword.length<6){window.toast("Password must be at least 6 characters","error"),l(!1);return}}const j=localStorage.getItem("adminToken"),w="https://pluginsight.vercel.app",i={name:c.name,email:c.email},x=await fetch(`${w}/api/users/${s.id||s._id}`,{method:"PUT",headers:{Authorization:`Bearer ${j}`,"Content-Type":"application/json"},body:JSON.stringify(i)});if(!x.ok)throw new Error(`HTTP error! status: ${x.status}`);const D=await x.json();if(!D.success){window.toast(D.message||"Failed to update profile","error"),l(!1);return}if(c.newPassword){const M={newPassword:c.newPassword},U=await fetch(`${w}/api/users/${s.id||s._id}/reset-password`,{method:"PUT",headers:{Authorization:`Bearer ${j}`,"Content-Type":"application/json"},body:JSON.stringify(M)});if(!U.ok)throw new Error(`HTTP error! status: ${U.status}`);const R=await U.json();if(!R.success){window.toast(R.message||"Failed to update password","error"),l(!1);return}}const P={...s,...D.user};localStorage.setItem("adminUser",JSON.stringify(P)),window.toast("Profile updated successfully","success"),a(!1),d(M=>({...M,name:P.name,email:P.email,newPassword:"",confirmPassword:""})),window.location.reload()}catch(j){console.error("Error updating profile:",j),j.name==="TypeError"&&j.message.includes("Failed to fetch")?window.toast("Unable to connect to server. Please check if the backend is running.","error"):window.toast("Failed to update profile","error")}finally{l(!1)}},$=()=>{d({name:(s==null?void 0:s.name)||"",email:(s==null?void 0:s.email)||"",newPassword:"",confirmPassword:""}),a(!1)},b=k=>{d({...c,[k.target.name]:k.target.value})},f=k=>{switch(k){case"superadmin":return e.jsx(Be,{className:"h-5 w-5 text-yellow-600"});case"admin":return e.jsx(Be,{className:"h-5 w-5 text-blue-600"});default:return e.jsx(De,{className:"h-5 w-5 text-gray-600"})}},L=k=>{const j={superadmin:"bg-yellow-100 text-yellow-800 border-yellow-200",admin:"bg-blue-100 text-blue-800 border-blue-200",member:"bg-gray-100 text-gray-800 border-gray-200"};return e.jsxs("span",{className:`inline-flex items-center px-3 py-1 rounded-full text-sm font-medium border ${j[k]}`,children:[f(k),e.jsx("span",{className:"ml-2 capitalize",children:k})]})};return s?e.jsx("div",{className:"min-h-screen bg-gray-50",children:e.jsxs("div",{className:"space-y-6",children:[e.jsxs("div",{className:"p-4",children:[e.jsx("h1",{className:"text-2xl font-bold text-gray-900",children:"Profile Settings"}),e.jsx("p",{className:"text-sm text-gray-600",children:"Manage your account information and preferences"})]}),e.jsx("div",{className:"",children:e.jsx("div",{className:"max-w-4xl mx-auto",children:e.jsxs("div",{className:"grid grid-cols-1 lg:grid-cols-3 gap-8",children:[e.jsx("div",{className:"lg:col-span-1",children:e.jsx("div",{className:"bg-gradient-to-r from-blue-600 to-purple-600 rounded-xl shadow-sm border border-gray-100 p-6",children:e.jsxs("div",{className:"text-center",children:[e.jsx("div",{className:"h-24 w-24 rounded-full bg-white/20 flex items-center justify-center mx-auto mb-4",children:e.jsx(De,{className:"h-12 w-12 text-white"})}),e.jsx("h3",{className:"text-xl font-semibold text-white mb-2",children:s==null?void 0:s.name}),e.jsx("p",{className:"text-blue-100 mb-4",children:s==null?void 0:s.email}),L(s==null?void 0:s.role)]})})}),e.jsx("div",{className:"lg:col-span-2",children:e.jsxs("div",{className:"bg-white rounded-xl shadow-sm border border-gray-100 p-6",children:[e.jsxs("div",{className:"flex items-center justify-between mb-6",children:[e.jsx("h2",{className:"text-xl font-semibold text-gray-900",children:"Personal Information"}),n?e.jsxs("button",{onClick:$,className:"flex items-center space-x-2 px-4 py-2 bg-gray-50 text-gray-600 hover:bg-gray-100 rounded-lg transition-colors",children:[e.jsx(ze,{className:"h-4 w-4"}),e.jsx("span",{children:"Cancel"})]}):e.jsxs("button",{onClick:()=>a(!0),className:"flex items-center space-x-2 px-4 py-2 text-blue-600 hover:bg-blue-50 rounded-lg transition-colors",children:[e.jsx(ns,{className:"h-4 w-4"}),e.jsx("span",{children:"Edit"})]})]}),e.jsxs("form",{onSubmit:y,className:"space-y-6",children:[e.jsxs("div",{children:[e.jsxs("label",{className:"flex items-center text-sm font-medium text-gray-700 mb-2",children:[e.jsx(De,{className:"h-4 w-4 mr-2"}),"Full Name"]}),e.jsx("input",{type:"text",name:"name",value:c.name,onChange:b,disabled:!n,required:!0,className:"w-full px-4 py-3 border border-gray-300 rounded-lg focus:ring-2 focus:ring-blue-500 focus:border-transparent disabled:bg-gray-50 disabled:text-gray-500"})]}),e.jsxs("div",{children:[e.jsxs("label",{className:"flex items-center text-sm font-medium text-gray-700 mb-2",children:[e.jsx(as,{className:"h-4 w-4 mr-2"}),"Email Address"]}),e.jsx("input",{type:"email",name:"email",value:c.email,onChange:b,disabled:!n,required:!0,className:"w-full px-4 py-3 border border-gray-300 rounded-lg focus:ring-2 focus:ring-blue-500 focus:border-transparent disabled:bg-gray-50 disabled:text-gray-500"})]}),e.jsxs("div",{children:[e.jsxs("label",{className:"flex items-center text-sm font-medium text-gray-700 mb-2",children:[e.jsx(Be,{className:"h-4 w-4 mr-2"}),"Role"]}),e.jsx("div",{className:"w-full px-4 py-3 border border-gray-300 rounded-lg bg-gray-50 text-gray-500",children:e.jsx("span",{className:"capitalize",children:s==null?void 0:s.role})}),e.jsx("p",{className:"text-xs text-gray-500 mt-1",children:"Role can only be changed by administrators"})]}),n&&e.jsxs("div",{className:"border-t border-gray-200 pt-6",children:[e.jsx("h3",{className:"text-lg font-medium text-gray-900 mb-4",children:"Change Password"}),e.jsxs("div",{className:"space-y-4",children:[e.jsxs("div",{children:[e.jsx("label",{className:"block text-sm font-medium text-gray-700 mb-2",children:"New Password"}),e.jsx("input",{type:"password",name:"newPassword",value:c.newPassword,onChange:b,className:"w-full px-4 py-3 border border-gray-300 rounded-lg focus:ring-2 focus:ring-blue-500 focus:border-transparent",placeholder:"Enter new password"})]}),e.jsxs("div",{children:[e.jsx("label",{className:"block text-sm font-medium text-gray-700 mb-2",children:"Confirm New Password"}),e.jsx("input",{type:"password",name:"confirmPassword",value:c.confirmPassword,onChange:b,className:"w-full px-4 py-3 border border-gray-300 rounded-lg focus:ring-2 focus:ring-blue-500 focus:border-transparent",placeholder:"Confirm new password"})]})]})]}),n&&e.jsx("div",{className:"flex justify-end pt-6",children:e.jsxs("button",{type:"submit",disabled:p,className:"flex items-center space-x-2 px-6 py-3 bg-blue-50 text-blue-600 rounded-lg hover:bg-blue-100 transition-colors disabled:opacity-50 disabled:cursor-not-allowed",children:[e.jsx(ls,{className:"h-4 w-4"}),e.jsx("span",{children:p?"Saving...":"Save"})]})})]})]})})]})})})]})}):e.jsx("div",{className:"flex items-center justify-center min-h-screen",children:e.jsxs("div",{className:"text-center",children:[e.jsx("div",{className:"animate-spin rounded-full h-12 w-12 border-b-2 border-blue-600 mx-auto"}),e.jsx("p",{className:"mt-4 text-gray-600",children:"Loading profile..."})]})})},kt=["p","br","strong","b","em","i","u","h1","h2","h3","h4","h5","h6","ul","ol","li","blockquote","pre","code","a","img","div","span","table","thead","tbody","tr","td","th","iframe","video","source"],St={a:["href","title","target","rel"],img:["src","alt","title","width","height","class"],iframe:["src","width","height","frameborder","allowfullscreen","title","class"],video:["src","width","height","controls","autoplay","muted","loop","poster","class"],source:["src","type"],div:["class","id"],span:["class","id"],p:["class"],h1:["class"],h2:["class"],h3:["class"],h4:["class"],h5:["class"],h6:["class"],ul:["class"],ol:["class"],li:["class"],table:["class"],thead:["class"],tbody:["class"],tr:["class"],td:["class"],th:["class"],blockquote:["class"],pre:["class"],code:["class"]};function At(s){if(!s)return"";const n=document.createElement("div");n.innerHTML=s;function a(l){const c=l.tagName.toLowerCase();if(!kt.includes(c)){l.remove();return}if(c==="iframe"){const b=l.getAttribute("src"),f=l.getAttribute("title")||"Video content",L=document.createElement("div");L.className="bg-gradient-to-r from-blue-50 to-indigo-50 border border-blue-200 rounded-lg p-6 text-center my-4";let k="Video",j=`<svg class="w-8 h-8 text-blue-600" fill="none" stroke="currentColor" viewBox="0 0 24 24">
        <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M15 10l4.553-2.276A1 1 0 0121 8.618v6.764a1 1 0 01-1.447.894L15 14M5 18h8a2 2 0 002-2V8a2 2 0 00-2-2H5a2 2 0 00-2 2v8a2 2 0 002 2z"></path>
      </svg>`;b&&b.includes("youtube")?(k="YouTube Video",j=`<svg class="w-8 h-8 text-red-600" fill="currentColor" viewBox="0 0 24 24">
          <path d="M23.498 6.186a3.016 3.016 0 0 0-2.122-2.136C19.505 3.545 12 3.545 12 3.545s-7.505 0-9.377.505A3.017 3.017 0 0 0 .502 6.186C0 8.07 0 12 0 12s0 3.93.502 5.814a3.016 3.016 0 0 0 2.122 2.136c1.871.505 9.376.505 9.376.505s7.505 0 9.377-.505a3.015 3.015 0 0 0 2.122-2.136C24 15.93 24 12 24 12s0-3.93-.502-5.814zM9.545 15.568V8.432L15.818 12l-6.273 3.568z"/>
        </svg>`):b&&b.includes("vimeo")&&(k="Vimeo Video",j=`<svg class="w-8 h-8 text-blue-500" fill="currentColor" viewBox="0 0 24 24">
          <path d="M23.977 6.416c-.105 2.338-1.739 5.543-4.894 9.609-3.268 4.247-6.026 6.37-8.29 6.37-1.409 0-2.578-1.294-3.553-3.881L5.322 11.4C4.603 8.816 3.834 7.522 3.01 7.522c-.179 0-.806.378-1.881 1.132L0 7.197a315.065 315.065 0 0 0 4.192-3.729C5.978 2.4 7.333 1.718 8.222 1.718c2.104 0 3.391 1.262 3.863 3.783.508 2.27.861 3.683.861 4.235 0 1.288-.547 3.2-1.642 5.737-.832 1.96-1.747 2.94-2.747 2.94-.842 0-1.638-.79-2.387-2.37l-.318-.81c-.613-1.96-1.17-2.94-1.668-2.94-.498 0-1.225.562-2.178 1.688l-.951-1.4c1.588-1.96 3.176-2.94 4.764-2.94 1.588 0 2.823 1.225 3.706 3.676.883 2.45 1.225 3.676 1.225 3.676s.342 1.96 1.026 5.88c.684 3.92 1.026 5.88 1.026 5.88.342 1.96 1.026 2.94 2.052 2.94 1.026 0 2.394-.98 4.104-2.94 1.71-1.96 2.565-3.92 2.565-5.88z"/>
        </svg>`),L.innerHTML=`
        <div class="flex flex-col items-center space-y-3">
          ${j}
          <div>
            <h4 class="text-lg font-semibold text-gray-800 mb-1">${k}</h4>
            <p class="text-gray-600 text-sm mb-3">${f}</p>
            <a href="${b}" target="_blank" rel="noopener noreferrer"
               class="inline-flex items-center px-4 py-2 bg-blue-600 text-white rounded-lg hover:bg-blue-700 transition-colors">
              <svg class="w-4 h-4 mr-2" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M10 6H6a2 2 0 00-2 2v10a2 2 0 002 2h10a2 2 0 002-2v-4M14 4h6m0 0v6m0-6L10 14"></path>
              </svg>
              Watch Video
            </a>
          </div>
        </div>
      `,l.parentNode.replaceChild(L,l);return}const d=St[c]||[];Array.from(l.attributes).forEach(b=>{d.includes(b.name)||l.removeAttribute(b.name)}),Array.from(l.children).forEach(b=>a(b))}return Array.from(n.children).forEach(l=>a(l)),n.innerHTML}function Fe(s){if(!s)return"";let n=s.replace(/\[video\s+([^\]]+)\]/g,(a,p)=>{const l=p.match(/src="([^"]+)"/);return l?`<video controls><source src="${l[1]}" type="video/mp4"></video>`:""}).replace(/\[youtube\s+([^\]]+)\]/g,(a,p)=>{const l=p.match(/(?:id="|v=)([^"&\s]+)/);return l?`<iframe src="https://www.youtube.com/embed/${l[1]}" width="560" height="315" frameborder="0" allowfullscreen title="YouTube video"></iframe>`:""}).replace(/\[vimeo\s+([^\]]+)\]/g,(a,p)=>{const l=p.match(/id="?([^"\s]+)"?/);return l?`<iframe src="https://player.vimeo.com/video/${l[1]}" width="560" height="315" frameborder="0" allowfullscreen title="Vimeo video"></iframe>`:""}).replace(/https?:\/\/(?:www\.)?youtube\.com\/watch\?v=([a-zA-Z0-9_-]+)/g,(a,p)=>`<iframe src="https://www.youtube.com/embed/${p}" width="560" height="315" frameborder="0" allowfullscreen title="YouTube video"></iframe>`).replace(/https?:\/\/youtu\.be\/([a-zA-Z0-9_-]+)/g,(a,p)=>`<iframe src="https://www.youtube.com/embed/${p}" width="560" height="315" frameborder="0" allowfullscreen title="YouTube video"></iframe>`).replace(/https?:\/\/(?:www\.)?vimeo\.com\/(\d+)/g,(a,p)=>`<iframe src="https://player.vimeo.com/video/${p}" width="560" height="315" frameborder="0" allowfullscreen title="Vimeo video"></iframe>`);return At(n)}const Pt=()=>{const{slug:s}=bs(),n=He(),[a,p]=r.useState(null),[l,c]=r.useState(null),[d,y]=r.useState(!0),[$,b]=r.useState(""),[f,L]=r.useState({}),[k,j]=r.useState(""),[w,i]=r.useState(!0),[x,D]=r.useState(!1),P=async()=>{try{i(!0);const g=await fetch(`https://api.wordpress.org/plugins/info/1.2/?action=plugin_information&slug=${a.slug}`);if(!g.ok)throw new Error("Failed to fetch plugin information");const v=await g.json();v.versions&&L(v.versions)}catch(g){console.error("Error fetching versions data:",g),L({})}finally{i(!1)}};r.useEffect(()=>{a&&P()},[a]),r.useEffect(()=>{M()},[s]);const M=async()=>{try{y(!0);const g=localStorage.getItem("adminToken"),v="https://pluginsight.vercel.app",[T,_]=await Promise.all([fetch(`${v}/api/plugins/${s}`,{headers:{Authorization:`Bearer ${g}`}}),fetch(`${v}/api/analytics/plugin-info/${s}`,{headers:{Authorization:`Bearer ${g}`}})]);if(!T.ok)throw new Error("Failed to fetch plugin details");const E=await T.json();if(!E.success){b(E.message||"Plugin not found");return}if(p(E.plugin),_.ok){const I=await _.json();I.success&&I.pluginInfo?c(I.pluginInfo):console.log("No plugin information found in database for:",s)}else console.log("Failed to fetch plugin information from database")}catch(g){console.error("Error fetching plugin details:",g),b("Failed to load plugin details")}finally{y(!1)}},U=g=>{if(!g)return"N/A";const v=g.match(/^(\d{4})-(\d{2})-(\d{2})/);if(!v)return"N/A";const[,T,_,E]=v;return`${E}-${_}-${T}`},R=g=>{const v=Math.round((g||0)/20);return[...Array(5)].map((T,_)=>e.jsx(Me,{className:`h-4 w-4 ${_<v?"text-yellow-400 fill-current":"text-gray-300"}`},_))},S=g=>l&&l[g]!==void 0&&l[g]!==null?l[g]:a!=null&&a.pluginData&&a.pluginData[g]!==void 0&&a.pluginData[g]!==null?a.pluginData[g]:null,h=g=>{const v=g.split(".");if(l){let T=l;for(const _ of v)if(T&&typeof T=="object"&&T[_]!==void 0)T=T[_];else{T=null;break}if(T!==null)return T}if(a!=null&&a.pluginData){let T=a.pluginData;for(const _ of v)if(T&&typeof T=="object"&&T[_]!==void 0)T=T[_];else{T=null;break}return T}return null};return d?e.jsx("div",{className:"min-h-screen bg-gray-50 flex items-center justify-center",children:e.jsx("div",{className:"animate-spin rounded-full h-12 w-12 border-b-2 border-blue-600"})}):$||!a?e.jsx("div",{className:"min-h-screen bg-gray-50 flex items-center justify-center",children:e.jsxs("div",{className:"text-center",children:[e.jsx("div",{className:"text-red-600 text-xl mb-4",children:$}),e.jsx("button",{onClick:()=>n(-1),className:"bg-blue-600 text-white px-4 py-2 rounded-lg hover:bg-blue-700 transition-colors",children:"Go Back"})]})}):e.jsxs("div",{className:"min-h-screen bg-gray-50",children:[e.jsx("div",{className:"bg-white shadow-sm border-b",children:e.jsx("div",{className:"max-w-7xl mx-auto px-6 py-4",children:e.jsxs("div",{className:"flex items-center justify-between",children:[e.jsxs("div",{className:"flex items-center space-x-4",children:[e.jsxs("button",{onClick:()=>n(-1),className:"flex items-center space-x-2 text-gray-600 hover:text-gray-900 transition-colors",children:[e.jsx(Hs,{className:"h-5 w-5"}),e.jsx("span",{children:"Back"})]}),e.jsx("div",{className:"h-6 w-px bg-gray-300"}),e.jsx("h1",{className:"text-2xl font-bold text-gray-900",children:"Plugin Details"})]}),e.jsxs("div",{className:"flex items-center space-x-3",children:[S("homepage")&&e.jsxs("button",{onClick:()=>window.open(S("homepage"),"_blank"),className:"flex items-center space-x-2 px-4 py-2 bg-blue-100 hover:bg-blue-200 text-blue-700 rounded-lg transition-colors",children:[e.jsx(Oe,{className:"h-4 w-4"}),e.jsx("span",{children:"Home page"})]}),e.jsxs("button",{onClick:()=>window.open(`https://wordpress.org/plugins/${a.slug}/`,"_blank"),className:"flex items-center space-x-2 px-4 py-2 bg-blue-100 hover:bg-blue-200 text-blue-700 rounded-lg transition-colors",children:[e.jsx(Oe,{className:"h-4 w-4"}),e.jsx("span",{children:"View on WordPress.org"})]})]})]})})}),e.jsx("div",{className:"max-w-7xl mx-auto px-6 py-8",children:e.jsxs("div",{className:"grid grid-cols-1 lg:grid-cols-3 gap-8",children:[e.jsxs("div",{className:"lg:col-span-2 space-y-6",children:[e.jsx("div",{className:"bg-white rounded-xl shadow-sm border border-gray-100 p-6",children:e.jsxs("div",{className:"flex items-start space-x-4",children:[h("icons.2x")&&e.jsx("img",{src:h("icons.2x"),alt:a.name,className:"w-16 h-16 rounded-lg"}),e.jsxs("div",{className:"flex-1",children:[e.jsx("h2",{className:"text-2xl font-bold text-gray-900 mb-2",children:a.name}),e.jsxs("div",{className:"flex items-center space-x-6",children:[e.jsxs("div",{className:"flex items-center space-x-1",children:[R(S("rating")),e.jsxs("span",{className:"text-sm text-gray-600 ml-2",children:["(",S("num_ratings")||0," ratings)"]})]}),e.jsxs("div",{className:"flex items-center space-x-1 text-sm text-gray-600",children:[e.jsx(xe,{className:"h-4 w-4"}),e.jsxs("span",{children:[(S("downloaded")||0).toLocaleString()," ","downloads"]})]})]})]})]})}),h("sections.description")&&e.jsxs("div",{className:"bg-white rounded-xl shadow-sm border border-gray-100 p-6",children:[e.jsx("h3",{className:"text-lg font-semibold text-gray-900 mb-4",children:"Description"}),e.jsx("div",{className:"prose max-w-none text-gray-700",dangerouslySetInnerHTML:{__html:Fe(h("sections.description"))}})]}),h("sections.installation")&&e.jsxs("div",{className:"bg-white rounded-xl shadow-sm border border-gray-100 p-6",children:[e.jsx("h3",{className:"text-lg font-semibold text-gray-900 mb-4",children:"Installation"}),e.jsx("div",{className:"prose max-w-none text-gray-700",dangerouslySetInnerHTML:{__html:Fe(h("sections.installation"))}})]}),h("sections.faq")&&e.jsxs("div",{className:"bg-white rounded-xl shadow-sm border border-gray-100 p-6",children:[e.jsx("h3",{className:"text-lg font-semibold text-gray-900 mb-4",children:"Frequently Asked Questions"}),e.jsx("div",{className:"prose max-w-none text-gray-700",dangerouslySetInnerHTML:{__html:Fe(h("sections.faq"))}})]}),h("sections.changelog")&&e.jsxs("div",{className:"bg-white rounded-xl shadow-sm border border-gray-100 p-6",children:[e.jsx("h3",{className:"text-lg font-semibold text-gray-900 mb-4",children:"Changelog"}),e.jsx("div",{className:"prose max-w-none text-gray-700 max-h-96 overflow-y-auto",dangerouslySetInnerHTML:{__html:Fe(h("sections.changelog"))}})]}),S("screenshots")&&Object.keys(S("screenshots")).length>0&&e.jsxs("div",{className:"bg-white rounded-xl shadow-sm border border-gray-100 p-6",children:[e.jsx("h3",{className:"text-lg font-semibold text-gray-900 mb-4",children:"Screenshots"}),e.jsx("div",{className:"grid grid-cols-1 md:grid-cols-2 gap-4",children:Object.entries(S("screenshots")).slice(0,6).map(([g,v])=>e.jsxs("div",{className:"space-y-2",children:[e.jsx("img",{src:v.src,alt:v.caption||`Screenshot ${g}`,className:"w-full h-48 object-cover rounded-lg border border-gray-200",loading:"lazy"}),v.caption&&e.jsx("p",{className:"text-sm text-gray-600",children:v.caption})]},g))})]})]}),e.jsxs("div",{className:"space-y-6",children:[e.jsxs("div",{className:"bg-white rounded-xl shadow-sm border border-gray-100 p-6",children:[e.jsx("h3",{className:"text-lg font-semibold text-gray-900 mb-4",children:"Plugin Information"}),e.jsxs("div",{className:"space-y-4",children:[e.jsxs("div",{className:"flex items-center justify-between",children:[e.jsx("span",{className:"text-sm text-gray-600",children:"Version"}),e.jsx("span",{className:"text-sm font-medium text-gray-900",children:S("version")||"N/A"})]}),e.jsxs("div",{className:"flex items-center justify-between",children:[e.jsx("span",{className:"text-sm text-gray-600",children:"Rank"}),e.jsxs("span",{className:"text-sm font-medium text-gray-900",children:["#",a.currentRank||"N/A"]})]}),e.jsxs("div",{className:"flex items-center justify-between",children:[e.jsx("span",{className:"text-sm text-gray-600",children:"Last Updated"}),e.jsx("span",{className:"text-sm font-medium text-gray-900",children:U(S("last_updated"))})]}),e.jsxs("div",{className:"flex items-center justify-between",children:[e.jsx("span",{className:"text-sm text-gray-600",children:"Added"}),e.jsx("span",{className:"text-sm font-medium text-gray-900",children:U(S("added"))})]}),e.jsxs("div",{className:"flex items-center justify-between",children:[e.jsx("span",{className:"text-sm text-gray-600",children:"Requires WP"}),e.jsx("span",{className:"text-sm font-medium text-gray-900",children:S("requires")||"N/A"})]}),e.jsxs("div",{className:"flex items-center justify-between",children:[e.jsx("span",{className:"text-sm text-gray-600",children:"Tested up to"}),e.jsx("span",{className:"text-sm font-medium text-gray-900",children:S("tested")||"N/A"})]}),e.jsxs("div",{className:"flex items-center justify-between",children:[e.jsx("span",{className:"text-sm text-gray-600",children:"PHP Version"}),e.jsx("span",{className:"text-sm font-medium text-gray-900",children:S("requires_php")||"N/A"})]}),e.jsxs("div",{className:"flex items-center justify-between",children:[e.jsx("span",{className:"text-sm text-gray-600",children:"Active Installs"}),e.jsx("span",{className:"text-sm font-medium text-gray-900",children:S("active_installs")?`${S("active_installs").toLocaleString()}+`:"N/A"})]})]})]}),e.jsxs("div",{className:"bg-white rounded-xl shadow-sm border border-gray-100 p-6 space-y-4",children:[e.jsx("h3",{className:"text-lg font-semibold text-gray-900",children:"Download"}),e.jsxs("div",{className:"space-y-4",children:[e.jsx("div",{className:"bg-white rounded-lg p-4 border-2 border-green-200",children:e.jsxs("div",{className:"flex items-center justify-between",children:[e.jsxs("span",{className:"text-sm font-medium text-gray-700 flex gap-1 items-center",children:["Current Version",e.jsx("span",{className:"text-xs bg-green-100 text-green-800 px-2 py-1 rounded-full",children:"Latest"})]}),e.jsxs("div",{className:"text-md font-bold text-gray-900",children:["v",S("version")||"N/A"]}),e.jsx("button",{onClick:()=>window.open(S("download_link"),"_blank"),className:"bg-green-600 hover:bg-green-700 text-white py-2 px-2 rounded-lg transition-colors flex items-center justify-center space-x-2",children:e.jsx(xe,{className:"h-4 w-4"})})]})}),e.jsx("div",{className:"bg-white rounded-lg p-4 border-2 border-gray-200",children:e.jsxs("div",{className:"flex items-center justify-between gap-2",children:[e.jsx("label",{className:"block text-sm font-medium text-gray-700 mb-2",children:"Older Versions"}),w?e.jsx("div",{className:"border border-gray-300 rounded-lg px-3 py-2 text-sm text-gray-500",children:"Loading versions..."}):e.jsxs("select",{value:k,onChange:g=>j(g.target.value),className:"w-full border border-gray-300 rounded-lg px-3 py-2 text-sm focus:outline-none focus:ring-2 focus:ring-blue-500 focus:border-transparent",children:[e.jsx("option",{value:"",children:"Select a version..."}),Object.entries(f).filter(([g])=>{var v;return g!==((v=a==null?void 0:a.pluginData)==null?void 0:v.version)}).sort((g,v)=>{const T=I=>I.split(".").map(Number),[_,E]=[T(g[0]),T(v[0])];for(let I=0;I<Math.max(_.length,E.length);I++){const O=(E[I]||0)-(_[I]||0);if(O!==0)return O}return 0}).slice(0,15).map(([g])=>e.jsxs("option",{value:g,children:["v",g]},g))]}),e.jsx("button",{onClick:g=>{g.preventDefault(),k&&(f!=null&&f[k])&&(D(!0),window.open(f[k],"_blank"),setTimeout(()=>D(!1),2e3))},disabled:!k||x||w,className:"bg-blue-600 hover:bg-blue-700 disabled:bg-gray-400 disabled:cursor-not-allowed text-white py-2 px-2 rounded-lg transition-colors flex items-center justify-center space-x-2",children:x?e.jsx(e.Fragment,{children:e.jsx("div",{className:"animate-spin rounded-full h-4 w-4 border-b-2 border-white"})}):e.jsx(e.Fragment,{children:e.jsx(xe,{className:"h-4 w-4"})})})]})})]}),S("donate_link")&&e.jsxs("button",{onClick:()=>window.open(S("donate_link"),"_blank"),className:"w-full bg-red-100 hover:bg-red-200 text-red-700 py-2 px-4 rounded-lg transition-colors flex items-center justify-center space-x-2",children:[e.jsx(zs,{className:"h-4 w-4"}),e.jsx("span",{children:"Donate"})]})]}),S("author")&&e.jsxs("div",{className:"bg-white rounded-xl shadow-sm border border-gray-100 p-6",children:[e.jsx("h3",{className:"text-lg font-semibold text-gray-900 mb-4",children:"Author"}),e.jsxs("div",{className:"flex items-center space-x-3",children:[e.jsx(De,{className:"h-8 w-8 text-gray-400"}),e.jsxs("div",{children:[e.jsx("div",{className:"font-medium text-gray-900",children:S("author").replace(/<[^>]*>/g,"")}),S("author_profile")&&e.jsx("a",{href:S("author_profile"),target:"_blank",rel:"noopener noreferrer",className:"text-sm text-blue-600 hover:text-blue-800",children:"View Profile"})]})]})]}),S("tags")&&Object.keys(S("tags")).length>0&&e.jsxs("div",{className:"bg-white rounded-xl shadow-sm border border-gray-100 p-6",children:[e.jsx("h3",{className:"text-lg font-semibold text-gray-900 mb-4",children:"Tags"}),e.jsx("div",{className:"flex flex-wrap gap-2",children:Object.keys(S("tags")).slice(0,10).map(g=>e.jsx("span",{className:"inline-flex items-center px-2.5 py-0.5 rounded-full text-xs font-medium bg-blue-100 text-blue-800",children:g},g))})]}),S("contributors")&&Object.keys(S("contributors")).length>0&&e.jsxs("div",{className:"bg-white rounded-xl shadow-sm border border-gray-100 p-6",children:[e.jsx("h3",{className:"text-lg font-semibold text-gray-900 mb-4",children:"Contributors"}),e.jsx("div",{className:"flex flex-wrap gap-4",children:Object.entries(S("contributors")).slice(0,10).map(([g,v])=>e.jsx("a",{href:v.profile,target:"_blank",rel:"noopener noreferrer",className:"flex items-center space-x-2 rounded-lg shadow-sm",children:e.jsx("img",{src:v.avatar,alt:v.display_name||g,title:v.display_name||g,className:"w-8 h-8 rounded-full"})},g))})]})]})]})})]})};function Ct(){return e.jsx(tt,{children:e.jsx(et,{children:e.jsx(js,{children:e.jsxs(ws,{children:[e.jsx(ue,{path:"/login",element:e.jsx(it,{})}),e.jsxs(ue,{path:"/*",element:e.jsx(at,{children:e.jsx(ot,{})}),children:[e.jsx(ue,{path:"dashboard",element:e.jsx(ht,{})})," ",e.jsx(ue,{path:"plugin-rank",element:e.jsx(gt,{})}),e.jsx(ue,{path:"keyword-analysis",element:e.jsx(ft,{})}),e.jsx(ue,{path:"analytics",element:e.jsx(yt,{})}),e.jsx(ue,{path:"users",element:e.jsx(wt,{})}),e.jsx(ue,{path:"settings",element:e.jsx(Nt,{})}),e.jsx(ue,{path:"profile",element:e.jsx(vt,{})}),e.jsx(ue,{path:"plugin-details/:slug",element:e.jsx(Pt,{})}),e.jsx(ue,{path:"",element:e.jsx(ss,{to:"/dashboard",replace:!0})})]})]})})})})}ms(document.getElementById("root")).render(e.jsx(r.StrictMode,{children:e.jsx(Ct,{})}));
